# 配置加载模块 (configLoader.js)

## 模块用途

配置加载模块负责管理应用配置，提供：
- 多层配置合并
- 环境变量处理
- 配置验证和默认值
- 动态配置加载

## 主要函数

### loadConfig(configPath)

加载和合并配置，是模块的核心函数。

**参数：**
- `configPath` (string): 自定义配置文件路径（可选）

**返回值：**
- `Object`: 合并后的完整配置对象

**加载顺序：**
1. 加载默认配置 (`config/default.config.js`)
2. 加载环境变量配置
3. 加载自定义配置文件（如果指定）
4. 验证最终配置

### loadDefaultConfig()

加载默认配置文件。

**返回值：**
- `Object`: 默认配置对象

**功能：**
- 读取 `config/default.config.js`
- 处理加载错误
- 提供基础配置结构

### loadEnvironmentConfig()

从环境变量加载配置。

**返回值：**
- `Object`: 环境变量配置对象

**支持的环境变量：**
- `OPENAI_API_KEY`: OpenAI API 密钥
- `OPENAI_API_URL`: API 端点地址
- `OPENAI_MODEL`: 使用的模型
- `OPENAI_MAX_TOKENS`: 最大 token 数
- `OPENAI_TEMPERATURE`: 温度参数
- `OPENAI_REJECT_UNAUTHORIZED`: SSL 证书验证（true/false）
- `PROMPT_ENABLE_COMPRESSION`: 提示词压缩（true/false）

### loadCustomConfig(configPath)

加载自定义配置文件。

**参数：**
- `configPath` (string): 配置文件路径

**返回值：**
- `Object`: 自定义配置对象

**支持的格式：**
- JavaScript 模块 (`.js`)
- JSON 文件 (`.json`)

### mergeConfigs(...configs)

合并多个配置对象。

**参数：**
- `...configs` (Object[]): 要合并的配置对象数组

**返回值：**
- `Object`: 合并后的配置对象

**合并策略：**
- 深度合并对象属性
- 后面的配置覆盖前面的配置
- 数组属性完全替换

### validateConfig(config)

验证配置的有效性。

**参数：**
- `config` (Object): 要验证的配置对象

**返回值：**
- `boolean`: 配置是否有效

**验证项目：**
- 必需字段检查
- 数据类型验证
- 值范围检查
- 依赖关系验证

## 配置结构

### 默认配置结构

```javascript
{
  project: {
    type: 'auto'
  },
  llm: {
    provider: 'openai',
    apiKey: null,
    apiUrl: 'https://api.openai.com/v1',
    model: 'gpt-4',
    maxTokens: 8192,
    temperature: 0.1,
    timeout: 60000
  },
  analysis: {
    largeChangeThreshold: 50,
    ignorePatterns: [...],
    fileImportance: {
      importantPatterns: [...],
      unimportantPatterns: [...]
    }
  }
}
```

### 环境变量映射

```javascript
{
  'OPENAI_API_KEY': 'llm.apiKey',
  'OPENAI_API_URL': 'llm.apiUrl',
  'OPENAI_MODEL': 'llm.model',
  'OPENAI_MAX_TOKENS': 'llm.maxTokens',
  'OPENAI_TEMPERATURE': 'llm.temperature',
  'OPENAI_REJECT_UNAUTHORIZED': 'llm.rejectUnauthorized',
  'PROMPT_ENABLE_COMPRESSION': 'prompt.enableCompression'
}
```

### 配置优先级

配置加载按以下优先级顺序进行：
1. **环境变量** - 最高优先级，会覆盖默认配置
2. **默认配置** - 来自 `src/default.config.js` 的基础配置

**注意事项：**
- 环境变量文件仅支持项目根目录的 `.env` 文件
- 不支持多环境配置文件（如 `.env.local`, `.env.production`）
- 数值类型的环境变量会自动转换（如 `OPENAI_MAX_TOKENS`）
- 布尔类型的环境变量仅支持 `true` 和 `false` 字符串
- 无效的数值或布尔值会被忽略，使用默认值

## 配置验证

### 必需配置

- `llm.apiKey`: OpenAI API 密钥
- `llm.apiUrl`: API 端点地址
- `llm.model`: 模型名称

### 可选配置

- `project.type`: 项目类型
- `llm.maxTokens`: 最大 token 数
- `llm.temperature`: 温度参数
- `analysis.*`: 分析相关配置

### 验证规则

```javascript
{
  'llm.apiKey': {
    required: true,
    type: 'string',
    pattern: /^sk-/
  },
  'llm.maxTokens': {
    type: 'number',
    min: 100,
    max: 32000
  },
  'llm.temperature': {
    type: 'number',
    min: 0,
    max: 2
  }
}
```

## 错误处理

### 配置文件错误

- 文件不存在：使用默认配置
- 语法错误：显示详细错误信息
- 权限错误：提示权限问题

### 验证错误

- 必需字段缺失：显示缺失字段
- 类型错误：显示期望类型
- 值超出范围：显示有效范围

### 环境变量错误

- 格式错误：显示正确格式
- 值无效：提供有效值示例

## 配置优先级

配置的优先级从高到低：

1. **自定义配置文件** - 通过 `--config` 参数指定
2. **环境变量** - `.env` 文件或系统环境变量
3. **默认配置** - `config/default.config.js`

## 动态配置

### 运行时修改

```javascript
const config = loadConfig();
config.llm.temperature = 0.2;
```

### 配置热重载

支持在运行时重新加载配置：

```javascript
const newConfig = loadConfig('./new-config.js');
```

## 配置示例

### 基本配置

```javascript
// my-config.js
module.exports = {
  llm: {
    model: 'gpt-3.5-turbo',
    maxTokens: 4096
  },
  analysis: {
    largeChangeThreshold: 100
  }
};
```

### 环境特定配置

```javascript
// production.config.js
module.exports = {
  llm: {
    timeout: 120000,
    maxTokens: 8192
  },
  analysis: {
    ignorePatterns: [
      'node_modules/**',
      'dist/**',
      'coverage/**'
    ]
  }
};
```

## 工具函数

### getConfigValue(config, path, defaultValue)

获取配置值。

**参数：**
- `config` (Object): 配置对象
- `path` (string): 配置路径（如 'llm.model'）
- `defaultValue` (any): 默认值

### setConfigValue(config, path, value)

设置配置值。

**参数：**
- `config` (Object): 配置对象
- `path` (string): 配置路径
- `value` (any): 要设置的值

### hasConfigValue(config, path)

检查配置值是否存在。

**参数：**
- `config` (Object): 配置对象
- `path` (string): 配置路径

**返回值：**
- `boolean`: 配置值是否存在

## 使用示例

```javascript
const configLoader = require('./configLoader');

// 加载默认配置
const config = configLoader.loadConfig();

// 加载自定义配置
const customConfig = configLoader.loadConfig('./my-config.js');

// 验证配置
if (!configLoader.validateConfig(config)) {
  throw new Error('配置无效');
}

// 使用配置
console.log('使用模型:', config.llm.model);
console.log('最大 tokens:', config.llm.maxTokens);
```
