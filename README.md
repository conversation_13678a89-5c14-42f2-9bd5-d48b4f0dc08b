# AI Analyzer

AI 代码变更分析工具 - 智能分析 Git 提交之间的代码差异，生成专业的上线变更总结报告。

## 功能特性

- 智能分析代码变更，识别文件角色和重要性
- 基于 OpenAI GPT 模型生成专业的变更总结报告
- 支持 React、Vue、Node.js、Next.js 等主流技术栈
- 自动保存分析日志和总结文件

## 安装

```bash
# 克隆项目
git clone <repository-url>
cd ai-analyzer

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入你的 API Key 和其他配置
```

## 使用方法

```bash
# 生成两次 commit 之间的代码变更总结
npm run summary <repo-path> <start-commit> <end-commit>

# 或直接使用 CLI
node src/cli.js summary <repo-path> <start-commit> <end-commit>

# 示例
npm run summary /path/to/your/project abc123 def456
node src/cli.js summary /path/to/your/project abc123 def456
```

工具运行后会自动生成：
- `logs/projectName-timestamp-startCommit-endCommit.log` - 分析过程日志
- `summary/projectName-timestamp-startCommit-endCommit.md` - AI 生成的变更总结报告

文件名格式说明：
- `projectName`: 项目目录名称（特殊字符会被清理为连字符）
- `timestamp`: 生成时间戳（ISO格式，冒号和点替换为连字符）
- `startCommit`: 起始提交ID的前7位
- `endCommit`: 结束提交ID的前7位

示例文件名：
- `logs/my-project-2025-08-08T12-30-45-123Z-abc1234-def5678.log`
- `summary/my-project-2025-08-08T12-30-45-123Z-abc1234-def5678.md`

## 环境变量配置

在 `.env` 文件中可以配置以下环境变量：

### 基础配置
- `OPENAI_API_KEY` - OpenAI API 密钥（必需）
- `OPENAI_API_URL` - API 地址（默认: https://api.openai.com/v1）
- `OPENAI_MODEL` - 使用的模型（默认: gpt-4）
- `OPENAI_MAX_TOKENS` - 最大 token 数（默认: 8192）
- `OPENAI_TEMPERATURE` - 温度参数（默认: 1.0）

### 高级配置
- `OPENAI_REJECT_UNAUTHORIZED` - SSL 证书验证（默认: true）
  - 设置为 `false` 可忽略 SSL 证书错误，仅用于开发环境
- `OPENAI_STREAM` - 流式响应模式（默认: false）
  - 设置为 `true` 启用流式响应，实时显示生成过程并写入文件
- `PROMPT_ENABLE_COMPRESSION` - 提示词压缩（默认: false）
  - 设置为 `true` 启用提示词压缩功能

## 文档

## 项目结构

```
src/
├── cli.js              # CLI 入口文件（简洁的命令行接口）
├── default.config.js   # 默认配置文件
├── utils/              # 通用工具模块
│   └── logger.js       # 通用日志记录组件
└── summary/            # 代码变更分析功能模块
    ├── analyzer.js         # 数据分析模块
    ├── configLoader.js     # 配置加载模块
    ├── fileNameUtils.js    # 文件名生成工具
    ├── git.js              # Git 操作模块
    ├── llm.js              # LLM 调用模块
    ├── logger.js           # 日志管理适配器（基于通用Logger）
    ├── parser.js           # 代码解析模块
    ├── prompt.js           # 提示词构建模块
    ├── summaryProcessor.js # 分析流程处理器（核心业务逻辑）
    ├── tokenUtils.js       # Token 计算工具（合并了 tokenCounter 和 tokenUtils）
    └── templates/          # 提示词模板目录
        ├── default-template.md     # 默认提示词模板
        ├── prompt-template.md      # 主提示词模板
        └── project-guidance/       # 项目类型指导模板
```

详细的技术文档请查看 [docs/](docs/) 目录：

- [cli.md](docs/cli.md) - CLI 模块
- [git.md](docs/git.md) - Git 操作模块
- [parser.md](docs/parser.md) - 代码解析模块
- [analyzer.md](docs/analyzer.md) - 数据分析模块
- [prompt.md](docs/prompt.md) - 提示词构建模块
- [llm.md](docs/llm.md) - LLM 调用模块
- [logger.md](docs/logger.md) - 日志管理模块
- [configLoader.md](docs/configLoader.md) - 配置加载模块

## 许可证

MIT License