/**
 * AI Analyzer 默认配置
 * 专注于前端项目分析，支持 OpenAI 及类 OpenAI 接口
 */

export default {
  // 项目配置
  project: {
    type: 'auto' // auto | react | vue | nextjs | nuxt | angular | svelte | node
  },

  // LLM 配置（仅支持 OpenAI 及类 OpenAI 接口）
  llm: {
    apiUrl: 'https://api.openai.com/v1',
    model: 'gpt-4',
    apiKey: '',
    maxTokens: 16000,
    temperature: 0.1,
    rejectUnauthorized: true,
    stream: false
  },

  // Token 计算配置
  tokenCounter: {
    // 是否启用缓存
    enableCache: true,
    // 缓存大小
    cacheSize: 1000,
    // 默认模型（如果 llm.model 未设置）
    defaultModel: 'gpt-4',
    // 压缩时的缓冲比例（0.1 = 10%）
    compressionBuffer: 0.1
  },

  // 分析配置
  analysis: {
    // 忽略的文件模式（用于分析时跳过的文件）
    ignorePatterns: [
      'node_modules/**',
      '.git/**',
      'dist/**',
      'build/**',
      '*.log',
      '.DS_Store',
      'Thumbs.db',
      'package-lock.json',
      'pnpm-lock.yaml',
      'yarn.lock',
      'LICENSE',
      'README.md'
    ],

    // 文件重要性判断配置（用于提示词生成时的文件筛选）
    fileImportance: {
      // 重要文件模式（这些文件会优先包含在提示词中）
      importantPatterns: [
        /\.(tsx?|jsx?)$/, // TypeScript/JavaScript 文件
        /\.(vue|svelte)$/, // 框架组件文件
        /package\.json$/, // 包配置文件
        /\.(config|conf)\./, // 配置文件
        /README/, // 文档文件
        /src\/.*\.(ts|js|tsx|jsx|vue)$/ // src 目录下的源码文件
      ],
      // 不重要文件模式（这些文件在提示词长度限制时会被过滤）
      unimportantPatterns: [
        /node_modules/, // 依赖包
        /\.git/, // Git 文件
        /dist\//, // 构建产物
        /build\//, // 构建产物
        /\.log$/ // 日志文件
      ]
    }
  },

  // 提示词配置
  prompt: {
    // 是否启用提示词压缩（当token数量超过限制时）
    enableCompression: false,
    // 压缩阈值比例（0.9 表示当达到90%限制时开始考虑压缩）
    compressionThreshold: 0.9
  }
}
