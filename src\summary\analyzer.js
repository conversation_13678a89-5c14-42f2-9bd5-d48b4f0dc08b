/**
 * 语义变更分析器
 * 构建用于 AI 分析的数据结构
 * 提取基础统计信息和结构化数据
 * 不进行复杂的语义分析（交由 AI 完成）
 */

import path from 'path'
import { logger } from './logger.js'

/**
 * 构建分析数据
 * @param {Array} enhancedDiffs - 增强版 diff 数据
 * @param {Array} parsedFiles - 解析后的文件结构
 * @param {Object} config - 配置对象
 * @returns {Object} 用于 AI 分析的数据结构
 */
async function buildAnalysisData (enhancedDiffs, parsedFiles, config = {}) {
  logger.info('开始构建分析数据')

  try {
    // 1. 计算基础统计信息
    const summary = calculateChangeStats(enhancedDiffs, config)
    logger.log(`📈 统计信息: ${summary.totalFiles} 个文件, +${summary.totalAddedLines} -${summary.totalDeletedLines}`)

    // 2. 合并文件数据
    const filesData = mergeFileData(enhancedDiffs, parsedFiles)
    logger.log(`📄 合并文件数据: ${filesData.length} 个文件`)

    // 3. 分析项目上下文
    const projectContext = {
      packageJsonChanges: analyzePackageChanges(enhancedDiffs),
      configFileChanges: analyzeConfigChanges(enhancedDiffs),
      frameworkInfo: analyzeFrameworkInfo(parsedFiles),
      dependencyImpact: analyzeDependencyImpact(enhancedDiffs, parsedFiles)
    }

    logger.log(`📦 项目上下文: ${projectContext.packageJsonChanges.length} 个依赖变更, ${projectContext.configFileChanges.length} 个配置变更`)

    const analysisData = {
      summary,
      filesData,
      projectContext
    }

    logger.success('分析数据构建完成')
    return analysisData

  } catch (error) {
    logger.error('构建分析数据失败', error)
    throw error
  }
}

/**
 * 计算文件变更统计
 * @param {Array} enhancedDiffs - 增强版 diff 数据
 * @param {Object} config - 配置对象
 * @returns {Object} 统计信息
 */
function calculateChangeStats (enhancedDiffs, config = {}) {
  const stats = {
    totalFiles: enhancedDiffs.length,
    totalAddedLines: 0,
    totalDeletedLines: 0,
    totalModifiedLines: 0,
    fileTypes: {},
    newFiles: [],
    deletedFiles: []
  }

  enhancedDiffs.forEach(file => {
    const { changeStats, filePath, status } = file

    // 累计行数统计
    stats.totalAddedLines += changeStats.addedLines || 0
    stats.totalDeletedLines += changeStats.deletedLines || 0
    stats.totalModifiedLines += changeStats.modifiedLines || 0

    // 文件类型统计
    const ext = path.extname(filePath).toLowerCase()
    if (ext) {
      const extName = ext.substring(1) // 移除点号
      stats.fileTypes[extName] = (stats.fileTypes[extName] || 0) + 1
    }

    // 文件状态分类
    switch (status) {
      case 'added':
        stats.newFiles.push(filePath)
        break
      case 'deleted':
        stats.deletedFiles.push(filePath)
        break
    }

    // 移除大规模变更识别逻辑，进行全量分析
  })

  return stats
}

/**
 * 分析包依赖变更
 * @param {Array} enhancedDiffs - 增强版 diff 数据
 * @returns {Array} 包依赖变更信息
 */
function analyzePackageChanges (enhancedDiffs) {
  const packageChanges = []

  // 查找 package.json 文件变更
  const packageJsonFile = enhancedDiffs.find(file =>
    path.basename(file.filePath) === 'package.json'
  )

  if (packageJsonFile) {
    try {
      const beforeContent = packageJsonFile.beforeContent || '{}'
      const afterContent = packageJsonFile.afterContent || '{}'

      const beforePackage = JSON.parse(beforeContent)
      const afterPackage = JSON.parse(afterContent)

      // 分析 dependencies 变更
      const beforeDeps = beforePackage.dependencies || {}
      const afterDeps = afterPackage.dependencies || {}

      // 分析 devDependencies 变更
      const beforeDevDeps = beforePackage.devDependencies || {}
      const afterDevDeps = afterPackage.devDependencies || {}

      // 合并所有依赖进行分析
      const allBeforeDeps = { ...beforeDeps, ...beforeDevDeps }
      const allAfterDeps = { ...afterDeps, ...afterDevDeps }

      // 检查新增的依赖
      Object.keys(allAfterDeps).forEach(name => {
        if (!allBeforeDeps[name]) {
          packageChanges.push({
            name,
            action: 'added',
            version: allAfterDeps[name],
            type: afterDeps[name] ? 'dependency' : 'devDependency'
          })
        } else if (allBeforeDeps[name] !== allAfterDeps[name]) {
          packageChanges.push({
            name,
            action: 'updated',
            oldVersion: allBeforeDeps[name],
            newVersion: allAfterDeps[name],
            type: afterDeps[name] ? 'dependency' : 'devDependency'
          })
        }
      })

      // 检查删除的依赖
      Object.keys(allBeforeDeps).forEach(name => {
        if (!allAfterDeps[name]) {
          packageChanges.push({
            name,
            action: 'removed',
            version: allBeforeDeps[name],
            type: beforeDeps[name] ? 'dependency' : 'devDependency'
          })
        }
      })

    } catch (error) {
      logger.warn(`解析 package.json 失败: ${error.message}`)
    }
  }

  return packageChanges
}

/**
 * 分析配置文件变更
 * @param {Array} enhancedDiffs - 增强版 diff 数据
 * @returns {Array} 配置文件变更信息
 */
function analyzeConfigChanges (enhancedDiffs) {
  const configChanges = []

  // 定义配置文件模式
  const configPatterns = [
    { pattern: /tsconfig\.json$/, type: 'TypeScript配置' },
    { pattern: /webpack\.config\.(js|ts)$/, type: 'Webpack配置' },
    { pattern: /vite\.config\.(js|ts)$/, type: 'Vite配置' },
    { pattern: /next\.config\.(js|ts)$/, type: 'Next.js配置' },
    { pattern: /nuxt\.config\.(js|ts)$/, type: 'Nuxt.js配置' },
    { pattern: /vue\.config\.js$/, type: 'Vue配置' },
    { pattern: /angular\.json$/, type: 'Angular配置' },
    { pattern: /\.eslintrc\.(js|json|yml|yaml)$/, type: 'ESLint配置' },
    { pattern: /\.prettierrc(\.(js|json|yml|yaml))?$/, type: 'Prettier配置' },
    { pattern: /babel\.config\.(js|json)$/, type: 'Babel配置' },
    { pattern: /jest\.config\.(js|ts)$/, type: 'Jest配置' },
    { pattern: /cypress\.config\.(js|ts)$/, type: 'Cypress配置' },
    { pattern: /tailwind\.config\.(js|ts)$/, type: 'Tailwind配置' },
    { pattern: /postcss\.config\.js$/, type: 'PostCSS配置' },
    { pattern: /\.env(\..+)?$/, type: '环境变量' },
    { pattern: /dockerfile$/i, type: 'Docker配置' },
    { pattern: /docker-compose\.(yml|yaml)$/, type: 'Docker Compose配置' },
    { pattern: /\.github\/workflows\/.*\.yml$/, type: 'GitHub Actions' },
    { pattern: /\.gitignore$/, type: 'Git忽略规则' }
  ]

  enhancedDiffs.forEach(file => {
    const filePath = file.filePath.toLowerCase()

    configPatterns.forEach(({ pattern, type }) => {
      if (pattern.test(filePath)) {
        const changeType = getChangeType(file)

        configChanges.push({
          file: file.filePath,
          type,
          status: file.status,
          changeType,
          changeStats: file.changeStats
        })
      }
    })
  })

  return configChanges
}

/**
 * 分析变更类型
 * @param {Object} file - 文件变更信息
 * @returns {string} 变更类型
 */
function getChangeType (file) {
  const { changeStats } = file
  const totalChanges = (changeStats.addedLines || 0) + (changeStats.deletedLines || 0)

  if (changeStats.addedLines > changeStats.deletedLines * 2) {
    return '大量新增'
  } else if (changeStats.deletedLines > changeStats.addedLines * 2) {
    return '大量删除'
  } else if (totalChanges > 50) {
    return '大规模修改'
  } else if (totalChanges > 10) {
    return '中等修改'
  } else {
    return '小幅修改'
  }
}

/**
 * 合并文件结构信息
 * @param {Array} enhancedDiffs - 增强版 diff 数据
 * @param {Array} parsedFiles - 解析后的文件结构
 * @returns {Array} 合并后的文件数据
 */
function mergeFileData (enhancedDiffs, parsedFiles) {
  const filesData = []

  // 创建解析文件的映射，便于快速查找
  const parsedFilesMap = new Map()
  parsedFiles.forEach(parsed => {
    parsedFilesMap.set(parsed.filePath, parsed)
  })

  enhancedDiffs.forEach(diffFile => {
    const parsedFile = parsedFilesMap.get(diffFile.filePath)

    // 合并数据
    const fileData = {
      filePath: diffFile.filePath,
      status: diffFile.status,
      changeStats: diffFile.changeStats,
      diff: diffFile.diff,
      beforeContent: diffFile.beforeContent,
      afterContent: diffFile.afterContent,

      // 如果有解析结果，添加结构信息
      ...(parsedFile && {
        role: parsedFile.role,
        language: parsedFile.language,
        framework: parsedFile.framework,
        structure: parsedFile.structure
      }),

      // 添加文件分析信息
      analysis: analyzeFileImpact(diffFile, parsedFile)
    }

    filesData.push(fileData)
  })

  return filesData
}

/**
 * 分析单个文件的影响
 * @param {Object} diffFile - diff 文件信息
 * @param {Object} parsedFile - 解析后的文件信息
 * @returns {Object} 文件影响分析
 */
function analyzeFileImpact (diffFile, parsedFile) {
  const analysis = {
    riskLevel: 'low',
    impactAreas: [],
    suggestions: []
  }

  const { changeStats } = diffFile
  const totalChanges = (changeStats.addedLines || 0) + (changeStats.deletedLines || 0)

  // 风险等级评估
  if (totalChanges > 100) {
    analysis.riskLevel = 'high'
    analysis.suggestions.push('大规模变更，建议重点测试')
  } else if (totalChanges > 30) {
    analysis.riskLevel = 'medium'
    analysis.suggestions.push('中等规模变更，建议进行回归测试')
  }

  // 基于文件角色的影响分析
  if (parsedFile) {
    switch (parsedFile.role) {
      case 'API':
        analysis.impactAreas.push('接口变更')
        analysis.suggestions.push('需要测试API接口的兼容性')
        break
      case 'Component':
        analysis.impactAreas.push('UI组件')
        analysis.suggestions.push('需要测试组件的渲染和交互')
        break
      case 'Store':
        analysis.impactAreas.push('状态管理')
        analysis.suggestions.push('需要测试状态变更的影响')
        break
      case 'Config':
        analysis.impactAreas.push('配置变更')
        analysis.suggestions.push('需要验证配置的正确性')
        if (analysis.riskLevel === 'low') {
          analysis.riskLevel = 'medium' // 配置变更风险较高
        }
        break
      case 'Test':
        analysis.impactAreas.push('测试代码')
        analysis.suggestions.push('需要确保测试用例的有效性')
        break
    }

    // 基于导入导出的影响分析
    if (parsedFile.structure) {
      const { imports, exports } = parsedFile.structure

      if (exports && exports.length > 0) {
        analysis.impactAreas.push('导出变更')
        analysis.suggestions.push('检查依赖此模块的其他文件')
      }

      if (imports && imports.length > 5) {
        analysis.impactAreas.push('依赖复杂')
        analysis.suggestions.push('关注依赖模块的变更影响')
      }
    }
  }

  // 基于文件状态的分析
  switch (diffFile.status) {
    case 'added':
      analysis.impactAreas.push('新增文件')
      analysis.suggestions.push('确保新文件正确集成到项目中')
      break
    case 'deleted':
      analysis.impactAreas.push('删除文件')
      analysis.suggestions.push('确认没有其他文件依赖被删除的文件')
      if (analysis.riskLevel === 'low') {
        analysis.riskLevel = 'medium'
      }
      break
    case 'renamed':
      analysis.impactAreas.push('文件重命名')
      analysis.suggestions.push('检查所有引用此文件的地方')
      break
  }

  return analysis
}

/**
 * 分析框架信息
 * @param {Array} parsedFiles - 解析后的文件结构
 * @returns {Object} 框架信息
 */
function analyzeFrameworkInfo (parsedFiles) {
  const frameworkStats = {}
  const languageStats = {}
  const roleStats = {}

  parsedFiles.forEach(file => {
    // 统计框架使用
    if (file.framework && file.framework !== 'unknown') {
      frameworkStats[file.framework] = (frameworkStats[file.framework] || 0) + 1
    }

    // 统计语言使用
    if (file.language && file.language !== 'unknown') {
      languageStats[file.language] = (languageStats[file.language] || 0) + 1
    }

    // 统计文件角色
    if (file.role) {
      roleStats[file.role] = (roleStats[file.role] || 0) + 1
    }
  })

  // 确定主要框架
  const primaryFramework = Object.keys(frameworkStats).reduce((a, b) =>
    frameworkStats[a] > frameworkStats[b] ? a : b, 'unknown'
  )

  // 确定主要语言
  const primaryLanguage = Object.keys(languageStats).reduce((a, b) =>
    languageStats[a] > languageStats[b] ? a : b, 'unknown'
  )

  return {
    primaryFramework,
    primaryLanguage,
    frameworkStats,
    languageStats,
    roleStats,
    totalFiles: parsedFiles.length
  }
}

/**
 * 分析依赖影响
 * @param {Array} enhancedDiffs - 增强版 diff 数据
 * @param {Array} parsedFiles - 解析后的文件结构
 * @returns {Object} 依赖影响分析
 */
function analyzeDependencyImpact (enhancedDiffs, parsedFiles) {
  const impact = {
    affectedModules: [],
    crossFileReferences: [],
    potentialBreakingChanges: []
  }

  // 创建文件导入导出映射
  const exportMap = new Map() // 文件 -> 导出内容
  const importMap = new Map() // 文件 -> 导入内容

  parsedFiles.forEach(file => {
    if (file.structure) {
      if (file.structure.exports) {
        exportMap.set(file.filePath, file.structure.exports)
      }
      if (file.structure.imports) {
        importMap.set(file.filePath, file.structure.imports)
      }
    }
  })

  // 分析变更文件的影响
  enhancedDiffs.forEach(diffFile => {
    const exports = exportMap.get(diffFile.filePath)
    // const imports = importMap.get(diffFile.filePath)

    // 如果文件有导出，查找可能受影响的文件
    if (exports && exports.length > 0) {
      const affectedFiles = []

      // 查找导入此文件的其他文件
      parsedFiles.forEach(otherFile => {
        if (otherFile.filePath !== diffFile.filePath && otherFile.structure?.imports) {
          const hasImport = otherFile.structure.imports.some(imp => {
            // 简单的路径匹配（可以改进为更精确的模块解析）
            const relativePath = path.relative(
              path.dirname(otherFile.filePath),
              diffFile.filePath
            )
            return imp.module.includes(path.basename(diffFile.filePath, path.extname(diffFile.filePath))) ||
              imp.module.includes(relativePath.replace(/\\/g, '/'))
          })

          if (hasImport) {
            affectedFiles.push(otherFile.filePath)
          }
        }
      })

      if (affectedFiles.length > 0) {
        impact.affectedModules.push({
          changedFile: diffFile.filePath,
          affectedFiles,
          exportCount: exports.length
        })
      }
    }

    // 检查潜在的破坏性变更
    if (diffFile.status === 'deleted') {
      impact.potentialBreakingChanges.push({
        type: 'file_deletion',
        file: diffFile.filePath,
        risk: 'high',
        description: '文件被删除，可能影响依赖此文件的模块'
      })
    } else if (diffFile.status === 'renamed') {
      impact.potentialBreakingChanges.push({
        type: 'file_rename',
        file: diffFile.filePath,
        risk: 'medium',
        description: '文件被重命名，需要更新所有引用'
      })
    }
  })

  return impact
}

/**
 * 生成分析摘要
 * @param {Object} analysisData - 分析数据
 * @returns {Object} 分析摘要
 */
function generateAnalysisSummary (analysisData) {
  const { summary, filesData, projectContext } = analysisData

  return {
    overview: {
      totalFiles: summary.totalFiles,
      codeChanges: `+${summary.totalAddedLines} -${summary.totalDeletedLines}`,
      riskLevel: calculateOverallRisk(filesData),
      primaryFramework: projectContext.frameworkInfo?.primaryFramework || 'unknown'
    },
    highlights: {
      newFiles: summary.newFiles.length,
      deletedFiles: summary.deletedFiles.length,
      configChanges: projectContext.configFileChanges.length,
      dependencyChanges: projectContext.packageJsonChanges.length
    },
    recommendations: generateRecommendations(analysisData)
  }
}

/**
 * 计算整体风险等级
 * @param {Array} filesData - 文件数据
 * @returns {string} 风险等级
 */
function calculateOverallRisk (filesData) {
  let highRiskCount = 0
  let mediumRiskCount = 0

  filesData.forEach(file => {
    switch (file.analysis?.riskLevel) {
      case 'high':
        highRiskCount++
        break
      case 'medium':
        mediumRiskCount++
        break
    }
  })

  if (highRiskCount > 0) {
    return 'high'
  } else if (mediumRiskCount > filesData.length * 0.3) {
    return 'medium'
  } else {
    return 'low'
  }
}

/**
 * 生成建议
 * @param {Object} analysisData - 分析数据
 * @returns {Array} 建议列表
 */
function generateRecommendations (analysisData) {
  const recommendations = []
  const { summary, projectContext } = analysisData

  // 移除基于变更规模的建议，进行全量分析

  // 基于依赖变更的建议
  if (projectContext.packageJsonChanges.length > 0) {
    recommendations.push({
      type: 'dependency',
      priority: 'medium',
      message: `检测到 ${projectContext.packageJsonChanges.length} 个依赖变更，建议检查兼容性`
    })
  }

  // 基于配置变更的建议
  if (projectContext.configFileChanges.length > 0) {
    recommendations.push({
      type: 'configuration',
      priority: 'medium',
      message: `发现 ${projectContext.configFileChanges.length} 个配置文件变更，建议验证配置正确性`
    })
  }

  // 基于文件删除的建议
  if (summary.deletedFiles.length > 0) {
    recommendations.push({
      type: 'breaking_change',
      priority: 'high',
      message: `有 ${summary.deletedFiles.length} 个文件被删除，请确认没有其他模块依赖`
    })
  }

  return recommendations
}

export {
  buildAnalysisData,
  calculateChangeStats,
  analyzePackageChanges,
  analyzeConfigChanges,
  mergeFileData,
  analyzeFrameworkInfo,
  analyzeDependencyImpact,
  generateAnalysisSummary
}
