## Vue 项目特定分析指导

在分析 Vue 项目时，请特别关注：

### 组件分析
- **组件类型**: 区分选项式 API 和组合式 API
- **生命周期**: mounted、updated、destroyed 等钩子的使用
- **组件通信**: props、emit、provide/inject 的使用
- **插槽使用**: 具名插槽、作用域插槽的实现

### 响应式系统
- **数据绑定**: v-model、v-bind 的使用情况
- **计算属性**: computed 的优化和缓存
- **侦听器**: watch、watchEffect 的使用场景
- **响应式引用**: ref、reactive 的选择和使用

### 路由和状态
- **Vue Router**: 路由配置和导航守卫
- **Vuex/Pinia**: 状态管理模式的变更
- **模块化**: store 模块的组织和管理

### 模板和指令
- **模板语法**: 插值、指令的使用
- **自定义指令**: 指令的定义和使用场景
- **条件渲染**: v-if、v-show 的选择
- **列表渲染**: v-for 的优化和 key 的使用