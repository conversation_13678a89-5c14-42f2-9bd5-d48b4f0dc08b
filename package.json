{"name": "ai-analyzer", "version": "1.0.0", "description": "AI 代码变更分析工具 - 对两次 commit 之间的代码变更进行深度分析，生成结构化的上线变更总结", "type": "module", "main": "src/cli.js", "bin": {"ai-analyzer": "./src/cli.js"}, "scripts": {"start": "node src/cli.js", "summary": "node src/cli.js summary", "lint": "eslint src/ --fix"}, "keywords": ["ai", "code-analysis", "git", "commit", "diff", "llm", "code-review", "release-notes", "changelog"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=16.0.0"}, "dependencies": {"axios": "^1.5.0", "commander": "^11.0.0", "tiktoken": "^1.0.21"}, "devDependencies": {"eslint": "^8.57.1", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/ai-analyzer.git"}, "bugs": {"url": "https://github.com/your-username/ai-analyzer/issues"}, "homepage": "https://github.com/your-username/ai-analyzer#readme", "files": ["src/", "config/", "templates/", "README.md", "LICENSE"]}