## Node.js 项目特定分析指导

在分析 Node.js 项目时，请特别关注：

### API 设计
- **RESTful API**: 接口设计和 HTTP 方法的使用
- **GraphQL**: Schema 定义和 Resolver 实现
- **中间件**: Express/Koa 中间件的使用和顺序
- **错误处理**: 统一错误处理机制

### 数据库操作
- **ORM/ODM**: Sequelize、Mongoose、Prisma 等的使用
- **数据库迁移**: 数据库结构变更和版本管理
- **查询优化**: 索引使用和查询性能
- **事务处理**: 数据一致性保证

### 安全性
- **身份验证**: JWT、Session 的实现
- **权限控制**: RBAC、ACL 等权限模型
- **输入验证**: 参数校验和 SQL 注入防护
- **HTTPS**: SSL/TLS 配置

### 性能和监控
- **缓存策略**: Redis、内存缓存的使用
- **日志记录**: 日志级别和格式规范
- **性能监控**: APM 工具集成
- **负载均衡**: 集群和进程管理