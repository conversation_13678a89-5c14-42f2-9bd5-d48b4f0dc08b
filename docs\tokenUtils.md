# Token 工具模块 (tokenUtils.js)

## 模块用途

Token 工具模块提供简化的 token 计算接口，是对 tokenCounter 模块的封装，提供：
- 简化的函数接口
- 统一的错误处理
- 便捷的工具函数
- 缓存管理功能

## 主要函数

### countTokens(text, model)

计算文本的 token 数量，是最常用的函数。

**参数：**
- `text` (string): 要计算的文本
- `model` (string): 模型名称（可选，默认 'gpt-4'）

**返回值：**
- `Promise<number>`: token 数量

**示例：**
```javascript
const tokenCount = await countTokens('Hello, world!');
console.log(tokenCount); // 4
```

### estimateTokens(text)

快速估算文本的 token 数量（同步方法）。

**参数：**
- `text` (string): 要估算的文本

**返回值：**
- `number`: 估算的 token 数量

**示例：**
```javascript
const estimated = estimateTokens('Hello, world!');
console.log(estimated); // 4
```

### validateTokenLength(text, maxTokens, model)

验证文本长度是否超过限制。

**参数：**
- `text` (string): 要验证的文本
- `maxTokens` (number): 最大 token 数
- `model` (string): 模型名称（可选）

**返回值：**
```javascript
{
  tokenCount: number,      // 实际 token 数
  maxTokens: number,       // 最大 token 数
  isValid: boolean,        // 是否在限制内
  excess: number,          // 超出的 token 数
  percentage: string       // 使用百分比
}
```

**示例：**
```javascript
const validation = await validateTokenLength('长文本...', 1000);
if (!validation.isValid) {
  console.log(`超出 ${validation.excess} tokens`);
}
```

### countTokensBatch(texts, model)

批量计算多个文本的 token 数量。

**参数：**
- `texts` (Array<string>): 文本数组
- `model` (string): 模型名称（可选）

**返回值：**
- `Promise<Array<number>>`: token 数量数组

**示例：**
```javascript
const texts = ['文本1', '文本2', '文本3'];
const counts = await countTokensBatch(texts);
console.log(counts); // [10, 15, 12]
```

### getTokenStats(text, model)

获取详细的 token 统计信息。

**参数：**
- `text` (string): 要分析的文本
- `model` (string): 模型名称（可选）

**返回值：**
```javascript
{
  totalTokens: number,
  totalLength: number,
  model: string,
  encoding: string,
  method: string,
  paragraphs: Array,
  averageTokensPerParagraph: number,
  tokensPerCharacter: number
}
```

## 缓存管理函数

### getCacheStats()

获取缓存统计信息。

**返回值：**
```javascript
{
  totalCalculations: number,
  cacheHits: number,
  cacheMisses: number,
  cacheSize: number,
  cacheHitRate: string
}
```

### clearTokenCache()

清空 token 计算缓存。

**示例：**
```javascript
clearTokenCache();
console.log('缓存已清空');
```

### isCacheEnabled()

检查缓存是否启用。

**返回值：**
- `boolean`: 缓存是否启用

## 工具函数

### isTiktokenAvailable()

检查 tiktoken 库是否可用。

**返回值：**
- `boolean`: tiktoken 是否可用

**示例：**
```javascript
if (isTiktokenAvailable()) {
  console.log('使用精确计算');
} else {
  console.log('使用估算方法');
}
```

### getModelEncoding(model)

获取模型对应的编码方式。

**参数：**
- `model` (string): 模型名称

**返回值：**
- `string`: 编码名称

**示例：**
```javascript
const encoding = getModelEncoding('gpt-4');
console.log(encoding); // 'cl100k_base'
```

### getSupportedModels()

获取支持的模型列表。

**返回值：**
- `Array<string>`: 支持的模型名称数组

**示例：**
```javascript
const models = getSupportedModels();
console.log(models); // ['gpt-4', 'gpt-3.5-turbo', ...]
```

### isModelSupported(model)

检查模型是否支持。

**参数：**
- `model` (string): 模型名称

**返回值：**
- `boolean`: 模型是否支持

## 错误处理

### 自动降级

当 tiktoken 不可用时，自动使用估算方法：

```javascript
// 内部实现
async function countTokens(text, model) {
  if (isTiktokenAvailable()) {
    return await preciseCount(text, model);
  } else {
    return estimateTokens(text);
  }
}
```

### 错误类型

- **模型不支持**: 使用默认模型并记录警告
- **文本为空**: 返回 0
- **参数错误**: 抛出详细错误信息
- **计算失败**: 回退到估算方法

## 配置选项

模块读取全局配置中的 token 相关设置：

```javascript
{
  tokenCounter: {
    enableCache: true,
    cacheSize: 1000,
    defaultModel: 'gpt-4',
    compressionBuffer: 0.1
  }
}
```

## 性能建议

### 批量处理

对于多个文本，使用批量接口更高效：

```javascript
// 推荐
const counts = await countTokensBatch(texts);

// 不推荐
const counts = await Promise.all(
  texts.map(text => countTokens(text))
);
```

### 缓存利用

重复计算相同文本时会自动使用缓存：

```javascript
// 第一次计算
const count1 = await countTokens('Hello, world!');

// 第二次计算（使用缓存）
const count2 = await countTokens('Hello, world!');
```

### 内存管理

定期清理缓存以释放内存：

```javascript
// 在适当的时候清理缓存
if (getCacheStats().cacheSize > 500) {
  clearTokenCache();
}
```

## 使用示例

### 基本使用

```javascript
const { countTokens, estimateTokens } = require('./tokenUtils');

// 精确计算
const exact = await countTokens('Hello, world!', 'gpt-4');

// 快速估算
const estimate = estimateTokens('Hello, world!');
```

### 长度验证

```javascript
const { validateTokenLength } = require('./tokenUtils');

const text = '很长的提示词文本...';
const validation = await validateTokenLength(text, 4000, 'gpt-4');

if (!validation.isValid) {
  console.log(`文本过长，需要压缩 ${validation.excess} tokens`);
}
```

### 批量处理

```javascript
const { countTokensBatch } = require('./tokenUtils');

const prompts = [
  '提示词1...',
  '提示词2...',
  '提示词3...'
];

const tokenCounts = await countTokensBatch(prompts, 'gpt-4');
const totalTokens = tokenCounts.reduce((sum, count) => sum + count, 0);

console.log(`总计 ${totalTokens} tokens`);
```
