# Git 操作模块 (git.js)

## 模块用途

Git 操作模块负责与 Git 仓库交互，提供：
- Git 仓库验证和 commit 验证
- 获取 commit 信息和变更统计
- 提取文件差异和内容
- 执行 Git 命令并解析输出

## 主要函数

### getCommitInfo(repoPath, startCommit, endCommit)

获取两个 commit 之间的基本信息。

**参数：**
- `repoPath` (string): Git 仓库路径
- `startCommit` (string): 起始 commit ID
- `endCommit` (string): 结束 commit ID

**返回值：**
```javascript
{
  commitCount: number,      // commit 数量
  authors: Array<string>,   // 参与的作者列表
  timeSpan: string,         // 时间跨度描述
  branchInfo: string        // 分支信息
}
```

### getEnhancedDiff(repoPath, startCommit, endCommit)

获取增强版的文件差异信息，包含详细的变更统计和文件内容。

**参数：**
- `repoPath` (string): Git 仓库路径
- `startCommit` (string): 起始 commit ID
- `endCommit` (string): 结束 commit ID

**返回值：**
```javascript
Array<{
  filePath: string,                    // 文件路径
  status: string,                      // 变更状态：'added'|'modified'|'deleted'|'renamed'
  diff: string,                        // Git diff 内容
  changeStats: {
    addedLines: number,                // 新增行数
    deletedLines: number,              // 删除行数
    modifiedLines: number              // 修改行数
  },
  beforeContent: string,               // 变更前文件内容
  afterContent: string                 // 变更后文件内容
}>
```

### validateGitRepository(repoPath)

验证指定路径是否为有效的 Git 仓库。

**参数：**
- `repoPath` (string): 要验证的路径

**功能：**
- 检查路径是否存在
- 验证是否为 Git 仓库
- 抛出详细的错误信息

### validateCommit(repoPath, commitId)

验证 commit ID 是否在仓库中存在。

**参数：**
- `repoPath` (string): Git 仓库路径
- `commitId` (string): 要验证的 commit ID

**功能：**
- 检查 commit 是否存在
- 支持短 commit ID 和完整 commit ID
- 抛出详细的错误信息

## 辅助函数

### execGitCommand(command, repoPath)

执行 Git 命令并返回输出结果。

**参数：**
- `command` (string): 要执行的 Git 命令
- `repoPath` (string): Git 仓库路径

**返回值：**
- `string`: 命令输出结果，失败时返回 null

**功能：**
- 在指定目录执行 Git 命令
- 处理命令执行错误
- 返回标准输出内容

### parseChangeStats(diffOutput)

解析 Git diff 输出，提取变更统计信息。

**参数：**
- `diffOutput` (string): Git diff 命令的输出

**返回值：**
```javascript
{
  addedLines: number,      // 新增行数
  deletedLines: number,    // 删除行数
  modifiedLines: number    // 修改行数（估算值）
}
```

**解析逻辑：**
- 统计以 `+` 开头的行（新增）
- 统计以 `-` 开头的行（删除）
- 排除 diff 头部信息行
- 计算修改行数（新增和删除的较小值）

### getFileContent(repoPath, commitId, filePath)

获取指定 commit 中文件的内容。

**参数：**
- `repoPath` (string): Git 仓库路径
- `commitId` (string): commit ID
- `filePath` (string): 文件路径

**返回值：**
- `string`: 文件内容，文件不存在时返回空字符串

**功能：**
- 使用 `git show` 命令获取文件内容
- 处理文件不存在的情况
- 支持二进制文件检测

## 状态映射

模块将 Git 的文件状态标识映射为更易理解的状态：

```javascript
const statusMap = {
  'A': 'added',      // 新增文件
  'M': 'modified',   // 修改文件
  'D': 'deleted',    // 删除文件
  'R': 'renamed',    // 重命名文件
  'C': 'copied'      // 复制文件
};
```

## 错误处理

Git 模块实现了完善的错误处理：

- **仓库验证错误**: 路径不存在、不是 Git 仓库
- **Commit 验证错误**: commit ID 不存在、格式错误
- **命令执行错误**: Git 命令失败、权限问题
- **文件访问错误**: 文件不存在、编码问题

所有错误都会记录详细的上下文信息，便于问题诊断。

## 性能优化

- **批量操作**: 一次性获取所有文件的差异信息
- **内容缓存**: 避免重复获取相同文件内容
- **错误恢复**: 单个文件处理失败不影响整体流程
- **内存管理**: 及时释放大文件内容占用的内存
