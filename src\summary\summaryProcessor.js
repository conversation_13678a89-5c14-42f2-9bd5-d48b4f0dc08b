/**
 * 代码变更分析总结处理器
 * 包含分析流程的核心逻辑
 */

import fs from 'fs'
import path from 'path'
import * as configLoader from './configLoader.js'
import * as git from './git.js'
import * as parser from './parser.js'
import * as analyzer from './analyzer.js'
import * as prompt from './prompt.js'
import * as llm from './llm.js'
import { logger, createSummaryLogger } from './logger.js'
import { generateFileName } from './fileNameUtils.js'

/**
 * 过滤掉LLM响应中的thinking内容
 * @param {string} llmResult - 原始LLM响应
 * @returns {string} 过滤后的内容
 */
function filterThinkingContent (llmResult) {
  // 移除 <think>...</think> 标签及其内容
  return llmResult.replace(/<think>[\s\S]*?<\/think>/gi, '').trim()
}

/**
 * 保存 LLM 分析总结到文件
 * @param {string} repoPath - 仓库路径
 * @param {string} startCommit - 起始 commit
 * @param {string} endCommit - 结束 commit
 * @param {string} llmResult - LLM 分析结果
 * @returns {string} 保存的文件名
 */
function saveSummary (repoPath, startCommit, endCommit, llmResult) {
  const fileName = generateFileName(repoPath, startCommit, endCommit, 'md')

  // 确保 summary 目录存在
  const summaryDir = path.join(process.cwd(), 'summary')
  if (!fs.existsSync(summaryDir)) {
    fs.mkdirSync(summaryDir, { recursive: true })
  }

  const filePath = path.join(summaryDir, fileName)

  // 过滤掉thinking内容（仅在summary文件中过滤，logs中保留）
  const filteredResult = filterThinkingContent(llmResult)

  // 添加文件头信息
  const header = [
    '# 代码变更分析总结',
    '',
    `**生成时间**: ${new Date().toLocaleString('zh-CN')}`,
    `**对比范围**: ${startCommit} -> ${endCommit}`,
    `**文件名**: ${fileName}`,
    '',
    '---',
    ''
  ].join('\n')

  const content = header + filteredResult

  try {
    fs.writeFileSync(filePath, content, 'utf8')
    logger.log(`📄 分析总结已保存到: summary/${fileName}`)
    return fileName
  } catch (error) {
    logger.error('保存分析总结失败', error)
    return null
  }
}

/**
 * 创建流式写入器
 * @param {string} repoPath - 仓库路径
 * @param {string} startCommit - 起始 commit
 * @param {string} endCommit - 结束 commit
 * @returns {Object} 流式写入器对象
 */
function createStreamWriter (repoPath, startCommit, endCommit) {
  const fileName = generateFileName(repoPath, startCommit, endCommit, 'md')

  // 确保 summary 目录存在
  const summaryDir = path.join(process.cwd(), 'summary')
  if (!fs.existsSync(summaryDir)) {
    fs.mkdirSync(summaryDir, { recursive: true })
  }

  const filePath = path.join(summaryDir, fileName)

  // 创建文件头信息
  const header = [
    '# 代码变更分析总结',
    '',
    `**生成时间**: ${new Date().toLocaleString('zh-CN')}`,
    `**对比范围**: ${startCommit} -> ${endCommit}`,
    `**文件名**: ${fileName}`,
    '',
    '---',
    ''
  ].join('\n')

  // 写入文件头
  fs.writeFileSync(filePath, header, 'utf8')

  let fullContent = ''
  let isFirstWrite = true

  return {
    writeChunk: (chunk, currentFullContent) => {
      // 记录到日志（保留thinking内容）
      if (isFirstWrite) {
        logger.log('\n🌊 开始接收流式响应:')
        isFirstWrite = false
      }
      logger.appendToLog(chunk)

      // 更新完整内容
      fullContent = currentFullContent

      // 写入到summary文件（过滤thinking内容）
      const filteredContent = filterThinkingContent(fullContent)
      const content = header + filteredContent

      try {
        fs.writeFileSync(filePath, content, 'utf8')
      } catch (error) {
        logger.error('流式写入summary文件失败', error)
      }
    },

    finish: () => {
      logger.log(`\n📄 流式分析总结已保存到: summary/${fileName}`)
      return fileName
    },

    getFileName: () => fileName,
    getFullContent: () => fullContent
  }
}

/**
 * 主函数 - 执行完整的分析流程
 * @param {string} repoPath - 仓库路径
 * @param {string} startCommit - 起始 commit id
 * @param {string} endCommit - 结束 commit id
 * @returns {Object} JSON Object 格式的分析结果
 */
async function processSummary (repoPath, startCommit, endCommit) {
  try {
    // 初始化日志文件
    createSummaryLogger(repoPath, startCommit, endCommit)

    logger.log('🔍 开始分析代码变更...')
    logger.log(`📁 仓库路径: ${repoPath}`)
    logger.log(`🔄 对比范围: ${startCommit} -> ${endCommit}`)
    logger.log('')

    // 1. 加载配置文件
    logger.info('步骤 1/6: 加载配置文件')
    const config = await configLoader.loadConfig()
    logger.success('配置加载完成')
    logger.log('')

    // 2. 调用 git.js - getEnhancedDiff 获取增强版 diff
    logger.info('步骤 2/6: 获取增强版 diff')
    const enhancedDiffs = await git.getEnhancedDiff(repoPath, startCommit, endCommit)
    logger.log(`📊 获取到 ${enhancedDiffs.length} 个文件的变更信息`)

    logger.log('📄 详细变更信息:')
    enhancedDiffs.forEach((file, index) => {
      logger.log(`  ${index + 1}. ${file.filePath} (${file.status})`)
      logger.log(`     +${file.changeStats.addedLines} -${file.changeStats.deletedLines} ~${file.changeStats.modifiedLines}`)
    })
    logger.log('')

    // 获取 commit 信息
    logger.info('获取 commit 信息')
    const commitInfo = await git.getCommitInfo(repoPath, startCommit, endCommit)
    logger.log('')

    // 3. 调用 parser.js - parseFilesStructure 解析文件结构
    logger.info('步骤 3/6: 解析文件结构')
    const parsedFiles = await parser.parseFilesStructure(enhancedDiffs)
    logger.log(`📊 解析到 ${parsedFiles.length} 个文件的结构信息`)

    logger.log('📄 文件结构信息:')
    parsedFiles.forEach((file, index) => {
      logger.log(`  ${index + 1}. ${file.filePath}`)
      logger.log(`     角色: ${file.role || 'Unknown'} | 语言: ${file.language} | 框架: ${file.framework}`)
      if (file.structure.imports && file.structure.imports.length > 0) {
        logger.log(`     导入: ${file.structure.imports.map(imp => imp.module).join(', ')}`)
      }
      if (file.structure.exports && file.structure.exports.length > 0) {
        logger.log(`     导出: ${file.structure.exports.map(exp => exp.name).join(', ')}`)
      }
    })
    logger.success('文件结构解析完成')
    logger.log('')

    // 4. 调用 analyzer.js - buildAnalysisData 构建分析数据
    logger.info('步骤 4/6: 构建分析数据')
    const analysisData = await analyzer.buildAnalysisData(enhancedDiffs, parsedFiles, config)

    // 生成分析摘要
    const analysisSummary = analyzer.generateAnalysisSummary(analysisData)
    logger.log('📊 分析摘要:')
    logger.log(`  🎯 整体风险: ${analysisSummary.overview.riskLevel}`)
    logger.log(`  📦 主要框架: ${analysisSummary.overview.primaryFramework}`)
    logger.log(`  ➕ 新增文件: ${analysisSummary.highlights.newFiles} 个`)
    logger.log(`  ➖ 删除文件: ${analysisSummary.highlights.deletedFiles} 个`)
    logger.log(`  ⚙️  配置变更: ${analysisSummary.highlights.configChanges} 个`)
    logger.log(`  📦 依赖变更: ${analysisSummary.highlights.dependencyChanges} 个`)

    if (analysisSummary.recommendations.length > 0) {
      logger.log('💡 建议:')
      analysisSummary.recommendations.forEach((rec, _index) => {
        const priority = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢'
        logger.log(`  ${priority} ${rec.message}`)
      })
    }

    logger.success('分析数据构建完成')
    logger.log('')

    // 5. 调用 prompt.js - buildEnhancedPrompt 构建增强提示词
    logger.info('步骤 5/6: 构建增强提示词')
    const promptText = await prompt.buildEnhancedPrompt(commitInfo, analysisData, config)
    logger.success('提示词构建完成')
    logger.log('📝 提示词:')
    logger.log(promptText)
    logger.log('')

    // 6. 调用 llm.js - callLLM 获取总结
    logger.info('步骤 6/6: 调用 LLM 获取总结')

    // 验证 LLM 配置
    if (!llm.validateLLMConfig(config)) {
      throw new Error('LLM 配置无效，请检查 .env 文件中的配置')
    }

    // 调用 LLM 获取分析结果
    let llmResult = ''

    if (config.llm.stream) {
      // 流式响应模式
      logger.log('🌊 启用流式响应模式，实时写入文件...')

      // 创建流式写入器
      const streamWriter = createStreamWriter(repoPath, startCommit, endCommit)

      llmResult = await llm.callLLM(promptText, config, (chunk, fullContent) => {
        // 实时写入到日志和summary文件
        streamWriter.writeChunk(chunk, fullContent)
        // 实时显示到控制台
        process.stdout.write(chunk)
      })

      // 完成流式写入
      streamWriter.finish()

    } else {
      // 非流式响应模式
      llmResult = await llm.callLLM(promptText, config)

      // 7. 保存分析总结到文件
      saveSummary(repoPath, startCommit, endCommit, llmResult)
    }

    logger.success('LLM 分析完成')
    logger.log(`📝 LLM 分析结果长度: ${llmResult.length} 字符`)
    logger.log('')

    // 8. 输出总结到终端
    logger.separator('分析结果汇总')

    const result = {
      success: true,
      message: '分析完成',
      data: {
        commitInfo,
        analysisData,
        analysisSummary,
        llmAnalysis: llmResult, // 添加 LLM 分析结果
        // 保留原有的简化数据以兼容
        enhancedDiffs: enhancedDiffs.map(file => ({
          filePath: file.filePath,
          status: file.status,
          changeStats: file.changeStats
        })),
        summary: analysisData.summary
      }
    }

    logger.log(`📈 总计变更文件: ${result.data.summary.totalFiles}`)
    logger.log(`📊 代码行统计: +${result.data.summary.totalAddedLines} -${result.data.summary.totalDeletedLines} ~${result.data.summary.totalModifiedLines}`)
    logger.log(`👥 参与作者: ${commitInfo.authors.join(', ')}`)
    logger.log(`📅 时间范围: ${commitInfo.timeSpan}`)
    logger.log(`🎯 整体风险等级: ${analysisSummary.overview.riskLevel}`)
    logger.log(`📦 主要框架: ${analysisSummary.overview.primaryFramework}`)
    logger.log('')

    // 显示 LLM 分析结果
    logger.separator('🤖 AI 分析总结')
    logger.log(llmResult)
    logger.log('')

    logger.success('分析完成')

    // 完成日志记录
    logger.finish()

    return result
  } catch (error) {
    logger.error('分析失败', error)
    logger.finish()
    throw error
  }
}

export {
  processSummary,
  saveSummary,
  createStreamWriter,
  filterThinkingContent
}
