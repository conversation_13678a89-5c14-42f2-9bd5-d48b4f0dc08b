# 代码解析模块 (parser.js)

## 模块用途

代码解析模块负责分析代码文件的结构和语义，提供：
- 文件语言和框架检测
- AST 解析和结构提取
- 文件角色识别
- 导入导出关系分析

## 主要函数

### parseFilesStructure(enhancedDiffs)

解析文件结构信息，是模块的主入口函数。

**参数：**
- `enhancedDiffs` (Array): 增强版 diff 数据

**返回值：**
```javascript
Array<{
  filePath: string,        // 文件路径
  role: string,           // 文件角色
  language: string,       // 编程语言
  framework: string,      // 技术框架
  structure: {
    imports: Array,       // 导入信息
    exports: Array,       // 导出信息
    functions: Array,     // 函数列表
    components: Array     // 组件列表
  }
}>
```

### analyzeFileRole(filePath, structure, framework)

分析文件在项目中的角色。

**参数：**
- `filePath` (string): 文件路径
- `structure` (Object): 文件结构信息
- `framework` (string): 检测到的框架

**返回值：**
- `string`: 文件角色类型

**角色类型：**
- `'Component'`: React/Vue 组件文件
- `'Page'`: 页面文件
- `'Service'`: 服务层文件
- `'Utility'`: 工具函数文件
- `'Config'`: 配置文件
- `'Test'`: 测试文件
- `'Unknown'`: 未知类型

### detectLanguage(filePath)

根据文件扩展名检测编程语言。

**参数：**
- `filePath` (string): 文件路径

**返回值：**
- `string`: 语言类型

**支持的语言：**
- `'javascript'`: .js, .jsx 文件
- `'typescript'`: .ts, .tsx 文件
- `'vue'`: .vue 文件
- `'json'`: .json 文件
- `'yaml'`: .yml, .yaml 文件
- `'css'`: .css, .scss, .less 文件
- `'markdown'`: .md 文件
- `'unknown'`: 其他文件类型

### detectFramework(structure, filePath)

检测项目使用的技术框架。

**参数：**
- `structure` (Object): 文件结构信息
- `filePath` (string): 文件路径

**返回值：**
- `string`: 框架类型

**支持的框架：**
- `'react'`: React 项目
- `'vue'`: Vue 项目
- `'node'`: Node.js 项目
- `'unknown'`: 未知框架

## 解析函数

### parseFileContent(content, filePath)

解析文件内容，提取结构信息。

**参数：**
- `content` (string): 文件内容
- `filePath` (string): 文件路径

**返回值：**
```javascript
{
  imports: Array,      // 导入语句
  exports: Array,      // 导出语句
  functions: Array,    // 函数定义
  components: Array,   // 组件定义
  hooks: Array,        // React Hooks
  template: Object     // Vue 模板信息
}
```

### parseJavaScriptContent(content, filePath)

解析 JavaScript/TypeScript 文件内容。

**功能：**
- 提取 import/require 语句
- 识别函数定义
- 检测 React 组件
- 分析 export 语句

### parseVueContent(content, filePath)

解析 Vue 单文件组件。

**功能：**
- 提取 `<script>` 标签内容
- 分析 `<template>` 标签
- 识别组件使用关系
- 解析 Vue 特有语法

### parseImports(content)

解析导入语句。

**返回值：**
```javascript
Array<{
  module: string,      // 模块名称
  imports: Array,      // 导入的内容
  type: string        // 导入类型：'default'|'named'|'namespace'
}>
```

**支持的导入格式：**
- ES6 模块：`import ... from '...'`
- CommonJS：`require('...')`
- 动态导入：`import('...')`

### parseExports(content)

解析导出语句。

**返回值：**
```javascript
Array<{
  name: string,        // 导出名称
  type: string,        // 导出类型：'default'|'named'|'module.exports'
  kind: string         // 导出种类：'function'|'class'|'variable'
}>
```

### parseFunctions(content)

解析函数定义。

**返回值：**
```javascript
Array<{
  name: string,        // 函数名称
  type: string,        // 函数类型：'function'|'arrow'|'method'
  async: boolean,      // 是否为异步函数
  params: Array        // 参数列表
}>
```

## 框架特定解析

### parseComponents(content)

解析 React 组件。

**功能：**
- 识别函数组件
- 检测类组件
- 分析 JSX 元素使用

### parseHooks(content)

解析 React Hooks。

**返回值：**
- `Array<string>`: 使用的 Hook 名称列表

### parseVueTemplateComponents(templateContent)

解析 Vue 模板中的组件使用。

**参数：**
- `templateContent` (string): 模板内容

**返回值：**
- `Array<string>`: 使用的组件名称列表

## 工具函数

### isSupportedFileType(filePath)

检查文件类型是否支持解析。

**支持的文件类型：**
- JavaScript: .js, .jsx, .ts, .tsx
- Vue: .vue
- 配置文件: .json, .yaml, .yml
- 样式文件: .css, .scss, .less
- 文档文件: .md, .txt

### getFileExtension(filePath)

获取文件扩展名。

**返回值：**
- `string`: 小写的文件扩展名

## 解析策略

模块采用轻量级的正则表达式解析策略：

1. **快速解析**: 使用正则表达式而非完整的 AST 解析器
2. **容错性强**: 即使语法错误也能提取部分信息
3. **性能优化**: 避免重量级依赖，提升解析速度
4. **扩展性好**: 易于添加新的语言和框架支持

## 限制说明

- 基于正则表达式的解析可能无法处理复杂的语法结构
- 不支持动态生成的导入导出语句
- 对于压缩或混淆的代码解析效果有限
- 主要针对常见的前端项目结构优化
