#!/usr/bin/env node

/**
 * CLI 入口，处理命令行输入
 * 调用各模块完成分析并输出
 */

import { Command } from 'commander'
import { processSummary } from './summary/summaryProcessor.js'

const program = new Command()

// CLI 命令配置
program
  .name('ai-analyzer')
  .description('AI 代码变更分析工具')
  .version('1.0.0')

program
  .command('summary')
  .description('生成两次 commit 之间的代码变更总结')
  .argument('<repo-path>', '仓库路径')
  .argument('<start-commit>', '起始 commit ID')
  .argument('<end-commit>', '结束 commit ID')
  .action(async (repoPath, startCommit, endCommit) => {
    try {
      await processSummary(repoPath, startCommit, endCommit)
    } catch (error) {
      process.exit(1)
    }
  })

// 直接解析命令行参数
program.parse()

export {
  processSummary
}
