# Electron应用快速搭建指南

## 立即开始实施

### 第一步：准备工作

```bash
# 确保在项目根目录
cd c:\workspace\project-analytics

# 备份当前工作（可选）
git add .
git commit -m "backup before electron integration"
```

### 第二步：修改根目录配置

```bash
# 修改根目录 package.json，添加 workspaces 配置
```

需要在根目录 `package.json` 中添加：
```json
{
  "workspaces": [
    ".",
    "electron-app"
  ]
}
```

### 第三步：创建Electron应用

```bash
# 创建electron-app目录
mkdir electron-app
cd electron-app

# 创建package.json
npm init -y
```

### 第四步：安装依赖

```bash
# 在electron-app目录下安装依赖
npm install electron vue@next vue-router@4 element-plus simple-git
npm install -D vite @vitejs/plugin-vue electron-builder concurrently wait-on

# 添加workspace依赖
npm install ai-analyzer@workspace:*
```

### 第五步：创建基础文件

#### 1. electron.js (主进程)
```javascript
const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const { processSummary } = require('ai-analyzer');

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 开发环境加载Vite服务器，生产环境加载构建文件
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile('dist/index.html');
  }
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC 处理程序
ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory']
  });
  return result.filePaths[0];
});

ipcMain.handle('start-analysis', async (event, repoPath, startCommit, endCommit) => {
  try {
    const result = await processSummary(repoPath, startCommit, endCommit);
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});
```

#### 2. preload.js (预加载脚本)
```javascript
const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  selectFolder: () => ipcRenderer.invoke('select-folder'),
  startAnalysis: (repoPath, startCommit, endCommit) => 
    ipcRenderer.invoke('start-analysis', repoPath, startCommit, endCommit)
});
```

#### 3. public/index.html
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI 代码分析工具</title>
</head>
<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>
</html>
```

#### 4. src/main.js (Vue入口)
```javascript
import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import App from './App.vue';

const app = createApp(App);
app.use(ElementPlus);
app.mount('#app');
```

### 第六步：配置构建工具

#### vite.config.js
```javascript
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()],
  base: './',
  build: {
    outDir: 'dist'
  }
});
```

### 第七步：更新package.json脚本

```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build", 
    "electron": "electron .",
    "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"",
    "dist": "npm run build && electron-builder"
  }
}
```

### 第八步：测试运行

```bash
# 开发模式运行
npm run electron-dev

# 或者分别运行
npm run dev        # 终端1：启动Vite
npm run electron   # 终端2：启动Electron
```

## 验证清单

- [ ] 能够启动Electron窗口
- [ ] 能够选择文件夹
- [ ] 能够调用CLI分析功能
- [ ] 能够显示分析结果

## 下一步

完成基础搭建后，可以继续开发：
1. 完善Vue组件
2. 添加Git提交记录显示
3. 实现实时日志功能
4. 添加配置管理界面
