# Token 计数器模块 (tokenCounter.js)

## 模块用途

Token 计数器模块提供精确的 token 计算功能，支持：
- 基于 tiktoken 库的精确计算
- 多种 OpenAI 模型支持
- 缓存机制优化性能
- 批量处理和统计分析

## 主要类

### TokenCounter

Token 计数器的核心类。

**构造函数：**
```javascript
new TokenCounter(options)
```

**参数：**
- `options` (Object): 配置选项
  - `enableCache` (boolean): 是否启用缓存
  - `cacheSize` (number): 缓存大小
  - `defaultModel` (string): 默认模型

## 主要函数

### countTokens(text, model)

计算文本的 token 数量。

**参数：**
- `text` (string): 要计算的文本
- `model` (string): 模型名称（可选）

**返回值：**
```javascript
{
  count: number,
  model: string,
  encoding: string,
  method: string
}
```

### countTokensBatch(texts, model)

批量计算多个文本的 token 数量。

**参数：**
- `texts` (Array<string>): 文本数组
- `model` (string): 模型名称（可选）

**返回值：**
- `Array<Object>`: token 计算结果数组

### estimateTokens(text)

估算文本的 token 数量（同步方法）。

**参数：**
- `text` (string): 要估算的文本

**返回值：**
- `number`: 估算的 token 数量

**估算规则：**
- 英文：约 4 个字符 = 1 token
- 中文：约 1.5 个字符 = 1 token
- 标点符号：通常 1 个字符 = 1 token

### getTokenStats(text, model)

获取详细的 token 统计信息。

**参数：**
- `text` (string): 要分析的文本
- `model` (string): 模型名称（可选）

**返回值：**
```javascript
{
  totalTokens: number,
  totalLength: number,
  model: string,
  encoding: string,
  method: string,
  paragraphs: Array,
  averageTokensPerParagraph: number,
  tokensPerCharacter: number
}
```

### isAvailable()

检查 tiktoken 库是否可用。

**返回值：**
- `boolean`: tiktoken 是否可用

### clearCache()

清空 token 计算缓存。

### getCacheStats()

获取缓存统计信息。

**返回值：**
```javascript
{
  totalCalculations: number,
  cacheHits: number,
  cacheMisses: number,
  cacheSize: number,
  cacheHitRate: string
}
```

## 支持的模型

### GPT-4 系列
- `gpt-4`: GPT-4 基础模型
- `gpt-4-turbo`: GPT-4 Turbo 模型
- `gpt-4-32k`: GPT-4 32K 上下文模型

### GPT-3.5 系列
- `gpt-3.5-turbo`: GPT-3.5 Turbo 模型
- `gpt-3.5-turbo-16k`: GPT-3.5 Turbo 16K 模型

### 其他模型
- `text-davinci-003`: Davinci 003 模型
- `text-davinci-002`: Davinci 002 模型
- `code-davinci-002`: Code Davinci 模型

## 编码映射

不同模型使用不同的编码方式：

```javascript
{
  'gpt-4': 'cl100k_base',
  'gpt-4-turbo': 'cl100k_base',
  'gpt-3.5-turbo': 'cl100k_base',
  'text-davinci-003': 'p50k_base',
  'text-davinci-002': 'p50k_base',
  'code-davinci-002': 'p50k_base'
}
```

## 缓存机制

### 缓存策略

- 使用 LRU (Least Recently Used) 策略
- 基于文本内容和模型的哈希值作为缓存键
- 自动清理过期和超量的缓存项

### 缓存配置

```javascript
{
  enableCache: true,    // 启用缓存
  cacheSize: 1000,     // 最大缓存项数
  ttl: 3600000        // 缓存有效期（毫秒）
}
```

## 错误处理

### Tiktoken 不可用

当 tiktoken 库不可用时：
- 自动回退到估算方法
- 记录警告信息
- 返回估算结果并标记方法

### 模型不支持

当指定的模型不支持时：
- 使用默认模型 (gpt-4)
- 记录警告信息
- 继续执行计算

### 文本过长

当文本超过模型限制时：
- 返回错误信息
- 提供建议的处理方法
- 不执行计算

## 性能优化

### 批量处理

批量计算比单独计算更高效：
- 减少函数调用开销
- 优化内存使用
- 提高缓存命中率

### 内存管理

- 及时释放大文本对象
- 限制缓存大小
- 定期清理无用缓存

### 计算优化

- 优先使用缓存结果
- 避免重复编码初始化
- 使用高效的哈希算法

## 全局实例

模块导出一个默认的 TokenCounter 实例：

```javascript
const { defaultTokenCounter } = require('./tokenCounter');

// 直接使用默认实例
const result = await defaultTokenCounter.countTokens('Hello, world!');
```