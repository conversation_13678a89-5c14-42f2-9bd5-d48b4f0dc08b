/**
 * 通用日志记录组件
 * - 支持输出到控制台与文件
 * - 支持自定义头部信息
 * - 提供 log/info/warn/error/success/separator 等方法
 */

import fs from 'fs'
import path from 'path'

class Logger {
  /**
   * @param {Object} [options]
   * @param {string} [options.filePath] 指定日志文件完整路径（若提供将自动创建目录并写入头部）
   * @param {Object} [options.header] 头部信息配置，传入则在初始化时写入
   * @param {boolean} [options.enableConsole=true] 是否输出到控制台
   * @param {string} [options.locale='zh-CN'] 时间格式 locale
   */
  constructor (options = {}) {
    const { filePath, header, enableConsole = true, locale = 'zh-CN' } = options

    this.logFile = null
    this.enableConsole = enableConsole !== false
    this.locale = locale || 'zh-CN'

    if (filePath) {
      this.initLogFile(filePath, header)
    }
  }

  /**
   * 初始化/切换日志文件，并写入头部
   * @param {string} filePath 完整文件路径
   * @param {Object} [header]
   * @param {string} [header.title='日志'] 标题
   * @param {Object} [header.metadata] 额外键值信息，会按 key: value 输出
   * @param {string[]} [header.extraLines] 额外附加的多行文本
   * @param {string} [header.char='='] 分隔字符
   * @param {number} [header.length=80] 分隔线长度
   * @returns {string} 实际日志文件路径
   */
  initLogFile (filePath, header = {}) {
    const { title = '日志', metadata = {}, extraLines = [], char = '=', length = 80 } = header || {}

    const dir = path.dirname(filePath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }

    this.logFile = filePath

    const headerLines = []
    headerLines.push(char.repeat(length))
    if (title) headerLines.push(`${title}`)
    headerLines.push(char.repeat(length))
    headerLines.push(`生成时间: ${new Date().toLocaleString(this.locale)}`)
    Object.keys(metadata || {}).forEach(key => {
      headerLines.push(`${key}: ${metadata[key]}`)
    })
    ;(extraLines || []).forEach(line => headerLines.push(String(line)))
    headerLines.push('')

    try {
      fs.writeFileSync(this.logFile, headerLines.join('\n'), 'utf8')
    } catch (error) {
      // 若头部写入失败，仍允许后续 console 输出
      console.warn('⚠️  初始化日志文件失败:', error.message)
    }

    return this.logFile
  }

  /**
   * 基础记录方法
   * @param {string} message 文本内容
   * @param {boolean} [consoleOnly=false] 是否仅输出到控制台
   */
  log (message, consoleOnly = false) {
    if (this.enableConsole) {
      console.log(message)
    }

    if (!consoleOnly && this.logFile) {
      const timestamp = new Date().toLocaleString(this.locale)
      const logEntry = `[${timestamp}] ${message}\n`
      try {
        fs.appendFileSync(this.logFile, logEntry, 'utf8')
      } catch (error) {
        console.warn('⚠️  写入日志文件失败:', error.message)
      }
    }
  }

  /**
   * 信息
   * @param {string} message
   */
  info (message) {
    this.log(`📋 ${message}`)
  }

  /**
   * 成功
   * @param {string} message
   */
  success (message) {
    this.log(`✅ ${message}`)
  }

  /**
   * 警告
   * @param {string} message
   */
  warn (message) {
    this.log(`⚠️  ${message}`)
  }

  /**
   * 错误
   * @param {string} message
   * @param {Error} [error]
   */
  error (message, error = null) {
    const errorMsg = error ? `${message}: ${error.message}` : message
    this.log(`❌ ${errorMsg}`)

    if (error && error.stack && this.logFile) {
      const stackTrace = `错误堆栈:\n${error.stack}\n`
      try {
        fs.appendFileSync(this.logFile, stackTrace, 'utf8')
      } catch (writeError) {
        console.warn('⚠️  写入错误堆栈失败:', writeError.message)
      }
    }
  }

  /**
   * 分隔线
   * @param {string} [title=''] 标题
   * @param {string} [char='='] 分隔字符
   * @param {number} [length=80] 长度
   */
  separator (title = '', char = '=', length = 80) {
    if (title) {
      const padding = Math.max(0, length - title.length - 2)
      const leftPad = Math.floor(padding / 2)
      const rightPad = padding - leftPad
      const line = char.repeat(leftPad) + ` ${title} ` + char.repeat(rightPad)
      this.log(line)
    } else {
      this.log(char.repeat(length))
    }
  }

  /**
   * 直接追加内容到日志文件（不加时间戳）
   * @param {string} content
   */
  appendToLog (content) {
    if (this.logFile) {
      try {
        fs.appendFileSync(this.logFile, content, 'utf8')
      } catch (error) {
        console.warn('⚠️  追加日志内容失败:', error.message)
      }
    }
  }

  /**
   * 收尾
   */
  finish () {
    if (this.logFile) {
      const footer = [
        '',
        '='.repeat(80),
        `完成时间: ${new Date().toLocaleString(this.locale)}`,
        '='.repeat(80)
      ].join('\n')

      try {
        fs.appendFileSync(this.logFile, footer, 'utf8')
      } catch (error) {
        console.warn('⚠️  写入日志结尾失败:', error.message)
      }
    }
  }

  /**
   * 开关控制台输出
   * @param {boolean} enabled
   */
  setConsoleOutput (enabled) {
    this.enableConsole = Boolean(enabled)
  }

  /**
   * 获取当前日志文件路径
   * @returns {string|null}
   */
  getLogFile () {
    return this.logFile
  }
}

export { Logger }
