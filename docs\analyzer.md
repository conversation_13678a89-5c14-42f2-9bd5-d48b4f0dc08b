# 数据分析模块 (analyzer.js)

## 模块用途

数据分析模块负责对代码变更进行深度分析，提供：
- 变更统计和规模评估
- 依赖关系影响分析
- 配置文件变更检测
- 风险评估和建议生成

## 主要函数

### buildAnalysisData(enhancedDiffs, parsedFiles, config)

构建完整的分析数据，是模块的核心函数。

**参数：**
- `enhancedDiffs` (Array): 增强版 diff 数据
- `parsedFiles` (Array): 解析后的文件结构
- `config` (Object): 配置信息

**返回值：**
```javascript
{
  summary: {
    totalFiles: number,           // 总文件数
    totalAddedLines: number,      // 总新增行数
    totalDeletedLines: number,    // 总删除行数
    totalModifiedLines: number,   // 总修改行数
    fileTypes: Object,           // 文件类型分布
    largeChanges: Array,         // 大规模变更文件
    newFiles: Array,             // 新增文件
    deletedFiles: Array          // 删除文件
  },
  filesData: Array,              // 合并后的文件数据
  projectContext: {
    packageJsonChanges: Array,   // 依赖变更
    configFileChanges: Array,    // 配置文件变更
    frameworkInfo: Object,       // 框架信息
    dependencyImpact: Object     // 依赖影响分析
  }
}
```

### calculateChangeStats(enhancedDiffs, config)

计算文件变更统计信息。

**参数：**
- `enhancedDiffs` (Array): 增强版 diff 数据
- `config` (Object): 配置信息

**返回值：**
```javascript
{
  totalFiles: number,
  totalAddedLines: number,
  totalDeletedLines: number,
  totalModifiedLines: number,
  fileTypes: {
    'javascript': number,
    'typescript': number,
    // 其他文件类型统计
  },
  largeChanges: Array<{
    filePath: string,
    totalChanges: number,
    addedLines: number,
    deletedLines: number
  }>,
  newFiles: Array<string>,
  deletedFiles: Array<string>
}
```

### analyzePackageChanges(enhancedDiffs)

分析 package.json 的依赖变更。

**参数：**
- `enhancedDiffs` (Array): 增强版 diff 数据

**返回值：**
```javascript
Array<{
  name: string,              // 包名称
  action: string,            // 操作类型：'added'|'removed'|'updated'
  version: string,           // 版本号
  description: string,       // 变更描述
  type: string              // 依赖类型：'dependencies'|'devDependencies'
}>
```

### analyzeConfigChanges(enhancedDiffs)

分析配置文件变更。

**参数：**
- `enhancedDiffs` (Array): 增强版 diff 数据

**返回值：**
```javascript
Array<{
  file: string,              // 文件路径
  type: string,              // 配置类型
  status: string,            // 变更状态
  changeType: string,        // 变更类型
  changeStats: Object        // 变更统计
}>
```

**支持的配置文件类型：**
- Webpack 配置: `webpack.config.js`
- Babel 配置: `.babelrc`, `babel.config.js`
- TypeScript 配置: `tsconfig.json`
- ESLint 配置: `.eslintrc.*`
- Prettier 配置: `.prettierrc.*`
- Tailwind 配置: `tailwind.config.js`
- 环境变量: `.env*`
- Docker 配置: `Dockerfile`, `docker-compose.yml`

### analyzeDependencyImpact(enhancedDiffs, parsedFiles)

分析依赖关系影响。

**参数：**
- `enhancedDiffs` (Array): 增强版 diff 数据
- `parsedFiles` (Array): 解析后的文件结构

**返回值：**
```javascript
{
  affectedModules: Array<{
    filePath: string,
    exports: Array,
    affectedFiles: Array
  }>,
  crossFileReferences: Array<{
    from: string,
    to: string,
    type: string
  }>,
  potentialBreakingChanges: Array<{
    filePath: string,
    reason: string,
    severity: string
  }>
}
```

### generateAnalysisSummary(analysisData)

生成分析摘要。

**参数：**
- `analysisData` (Object): 完整的分析数据

**返回值：**
```javascript
{
  overview: {
    totalFiles: number,
    codeChanges: string,
    riskLevel: string,          // 'low'|'medium'|'high'
    primaryFramework: string
  },
  highlights: {
    largeChanges: number,
    newFiles: number,
    deletedFiles: number,
    configChanges: number,
    dependencyChanges: number
  },
  recommendations: Array<{
    type: string,
    priority: string,           // 'low'|'medium'|'high'
    message: string
  }>
}
```

## 辅助函数

### mergeFileData(enhancedDiffs, parsedFiles)

合并 diff 数据和解析数据。

**功能：**
- 将文件变更信息与结构信息合并
- 添加文件影响分析
- 生成统一的文件数据格式

### analyzeFrameworkInfo(parsedFiles)

分析项目框架信息。

**返回值：**
```javascript
{
  primaryFramework: string,    // 主要框架
  primaryLanguage: string,     // 主要语言
  frameworkStats: Object,      // 框架使用统计
  languageStats: Object,       // 语言使用统计
  roleStats: Object,           // 文件角色统计
  totalFiles: number          // 总文件数
}
```

### analyzeFileImpact(diffFile, parsedFile)

分析单个文件的影响。

**返回值：**
```javascript
{
  importance: string,          // 重要性：'high'|'medium'|'low'
  complexity: string,          // 复杂度：'high'|'medium'|'low'
  riskLevel: string,          // 风险级别：'high'|'medium'|'low'
  reasons: Array              // 评估原因
}
```

## 风险评估

### calculateOverallRisk(filesData)

计算整体风险级别。

**评估因素：**
- 大规模变更文件数量
- 核心文件修改情况
- 配置文件变更影响
- 依赖关系变更风险

**风险级别：**
- `'low'`: 小规模变更，影响范围有限
- `'medium'`: 中等规模变更，需要重点测试
- `'high'`: 大规模变更，存在较高风险

### generateRecommendations(analysisData)

生成测试和部署建议。

**建议类型：**
- `'testing'`: 测试相关建议
- `'dependency'`: 依赖相关建议
- `'configuration'`: 配置相关建议
- `'breaking_change'`: 破坏性变更建议

## 统计分析

### getChangeType(file)

获取文件变更类型。

**返回值：**
- `'major'`: 主要变更（>50% 代码修改）
- `'minor'`: 次要变更（10-50% 代码修改）
- `'patch'`: 补丁变更（<10% 代码修改）

### calculateChangeRatio(changeStats)

计算变更比例。

**参数：**
- `changeStats` (Object): 变更统计信息

**返回值：**
- `number`: 变更比例（0-1）

## 配置文件检测

模块能够识别和分析多种配置文件：

- **构建工具**: Webpack, Vite, Rollup
- **代码质量**: ESLint, Prettier, Stylelint
- **类型检查**: TypeScript, Flow
- **测试框架**: Jest, Mocha, Cypress
- **部署配置**: Docker, CI/CD 配置
- **环境配置**: .env 文件

## 性能优化

- **增量分析**: 只分析变更的文件
- **缓存机制**: 缓存分析结果避免重复计算
- **并行处理**: 支持多文件并行分析
- **内存管理**: 及时释放大对象占用的内存
