/**
 * Prompt 构建器
 * 构建增强版提示词，包含丰富的上下文信息
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { logger } from './logger.js'
import { validateTokenLength, countTokens } from './tokenUtils.js'
import defaultConfig from '../default.config.js'

// 获取当前文件的目录路径（ES模块中的__dirname替代方案）
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * 构建增强版提示词
 * @param {Object} commitInfo - commit 信息
 * @param {Object} analysisData - 分析数据
 * @param {Object} config - 配置信息
 * @returns {string} 构建好的提示词
 */
async function buildEnhancedPrompt (commitInfo, analysisData, config = {}) {
  try {
    logger.info('开始构建增强提示词')

    // 0. 验证配置
    const configValidation = validatePromptConfig(config)
    if (!configValidation.isValid) {
      logger.warn('提示词配置验证失败:')
      configValidation.errors.forEach(error => logger.warn(`  - ${error}`))
      logger.warn('将使用默认配置继续执行')
    }

    // 1. 加载提示词模板
    logger.log('  📄 加载提示词模板')
    const template = await loadPromptTemplate()

    // 2. 注入分析结果和上下文信息
    logger.log('  📊 注入分析数据和上下文信息')
    const prompt = injectContextData(template, commitInfo, analysisData, config)

    // 3. 根据项目类型调整提示词策略
    const detectedProjectType = detectProjectType(analysisData, config)
    logger.log(`  🎯 根据项目类型调整提示词: ${detectedProjectType}`)
    const enhancedPrompt = await adjustPromptByProjectType(prompt, detectedProjectType)

    // 4. 验证提示词长度
    const maxTokens = config.llm?.maxTokens || 4000
    const model = config.llm?.model || 'gpt-4'
    logger.log(`  📏 验证提示词长度 (最大 ${maxTokens} tokens, 模型: ${model})`)

    let finalPrompt = enhancedPrompt

    // 使用精确的 token 计算
    const tokenValidation = await validateTokenLength(finalPrompt, maxTokens, model)

    if (!tokenValidation.isValid) {
      logger.warn(`提示词长度超出限制 (${tokenValidation.tokenCount}/${maxTokens} tokens)，正在压缩`)
      finalPrompt = await compressPrompt(finalPrompt, maxTokens, model, config)

      // 重新验证压缩后的长度
      const compressedValidation = await validateTokenLength(finalPrompt, maxTokens, model)
      logger.log(`  📊 压缩后长度: ${compressedValidation.tokenCount} tokens (${compressedValidation.percentage}%)`)
    }

    const finalTokenCount = tokenValidation.isValid
      ? tokenValidation.tokenCount
      : (await countTokens(finalPrompt, model))

    logger.log(`  📊 最终提示词长度: ${finalPrompt.length} 字符 (${finalTokenCount} tokens, tiktoken)`)

    // 5. 清理空字段
    logger.log('  🧹 清理空字段')
    finalPrompt = cleanEmptyFields(finalPrompt)

    logger.success('增强提示词构建完成')
    return finalPrompt

  } catch (error) {
    logger.error('构建提示词失败', error)
    throw error
  }
}

/**
 * 加载提示词模板
 * @returns {string} 提示词模板内容
 */
async function loadPromptTemplate () {
  try {
    const templatePath = path.join(__dirname, './templates/prompt-template.md')
    const template = await fs.promises.readFile(templatePath, 'utf-8')
    logger.log(`    ✅ 成功加载模板文件: ${templatePath}`)
    return template
  } catch (error) {
    logger.warn(`加载提示词模板失败: ${error.message}，使用默认模板`)
    return getDefaultTemplate()
  }
}

/**
 * 注入上下文数据到模板
 * @param {string} template - 提示词模板
 * @param {Object} commitInfo - commit 信息
 * @param {Object} analysisData - 分析数据
 * @param {Object} config - 配置信息
 * @returns {string} 注入数据后的提示词
 */
function injectContextData (template, commitInfo, analysisData, config) {
  let prompt = template

  // 注入基本信息
  const projectType = detectProjectType(analysisData, config)
  const techStack = detectTechStack(analysisData, config)

  prompt = prompt.replace(/{projectType}/g, projectType)
  prompt = prompt.replace(/{techStack}/g, techStack)
  prompt = prompt.replace(/{startCommit}/g, commitInfo.startCommit || '')
  prompt = prompt.replace(/{endCommit}/g, commitInfo.endCommit || '')
  prompt = prompt.replace(/{timeSpan}/g, commitInfo.timeSpan || '')
  prompt = prompt.replace(/{authors}/g, (commitInfo.authors || []).join(', '))

  // 注入统计信息
  const summary = analysisData.summary || {}
  prompt = prompt.replace(/{totalFiles}/g, summary.totalFiles || 0)
  prompt = prompt.replace(/{totalAddedLines}/g, summary.totalAddedLines || 0)
  prompt = prompt.replace(/{totalDeletedLines}/g, summary.totalDeletedLines || 0)
  prompt = prompt.replace(/{totalModifiedLines}/g, summary.totalModifiedLines || 0)

  // 注入文件类型分布
  const fileTypesStr = formatFileTypes(summary.fileTypes || {})
  prompt = prompt.replace(/{fileTypes}/g, fileTypesStr)

  // 注入所有变更文件（过滤掉被忽略的文件）
  const allChangedFiles = []
  const filesData = analysisData.filesData || []
  filesData.forEach(file => {
    if (file.status !== 'unchanged' && isImportantFile(file.filePath, {})) {
      allChangedFiles.push(file.filePath)
    }
  })
  const allChangedFilesStr = allChangedFiles.join(', ') || '无'
  prompt = prompt.replace(/{allChangedFiles\.join\(', '\) \|\| '无'}/g, allChangedFilesStr)

  // 注入新增和删除文件
  const newFilesStr = (summary.newFiles || []).join(', ') || '无'
  const deletedFilesStr = (summary.deletedFiles || []).join(', ') || '无'
  prompt = prompt.replace(/{newFiles\.join\(', '\) \|\| '无'}/g, newFilesStr)
  prompt = prompt.replace(/{deletedFiles\.join\(', '\) \|\| '无'}/g, deletedFilesStr)

  // 注入详细文件变更信息
  prompt = injectFileDetails(prompt, analysisData)

  // 注入依赖和配置变更
  prompt = injectDependencyChanges(prompt, analysisData)

  // 注入代码差异
  prompt = injectCodeDiffs(prompt, analysisData)

  logger.log(`    📊 已注入数据: ${summary.totalFiles} 个文件, ${(commitInfo.authors || []).length} 个作者`)

  return prompt
}

/**
 * 根据项目类型调整提示词策略
 * @param {string} prompt - 基础提示词
 * @param {string} projectType - 项目类型
 * @returns {string} 调整后的提示词
 */
async function adjustPromptByProjectType (prompt, projectType) {
  const normalizedType = projectType.toLowerCase()

  // 支持的项目类型映射
  const typeMapping = {
    react: 'react',
    vue: 'vue',
    node: 'node',
    nextjs: 'nextjs',
    'next.js': 'nextjs',
    javascript: null,
    frontend: null
  }

  const mappedType = typeMapping[normalizedType]
  if (!mappedType) {
    return prompt
  }

  try {
    const guidancePath = path.join(__dirname, './templates/project-guidance', `${mappedType}.md`)
    const guidance = await fs.promises.readFile(guidancePath, 'utf-8')
    return prompt + '\n\n' + guidance
  } catch (error) {
    logger.warn(`无法加载项目类型指导文件: ${mappedType}.md`)
    return prompt
  }
}

/**
 * 获取默认提示词模板
 * @returns {string} 默认模板
 */
function getDefaultTemplate () {
  try {
    const defaultTemplatePath = path.join(__dirname, './templates/default-template.md')
    return fs.readFileSync(defaultTemplatePath, 'utf-8')
  } catch (error) {
    logger.warn(`无法加载默认模板文件: ${error.message}`)
    // 如果文件不存在，返回最基本的模板
    return '# AI 代码变更分析\n\n请分析以下代码变更并生成专业的上线总结报告。\n\n## 项目信息\n- 项目类型: {projectType}\n- 技术栈: {techStack}\n\n请基于以上信息生成结构化的中文上线变更总结。'
  }
}

/**
 * 验证提示词长度（已弃用，使用 tokenUtils.validateTokenLength）
 * @deprecated 请使用 tokenUtils.validateTokenLength
 * @param {string} prompt - 提示词
 * @param {number} maxTokens - 最大 token 数
 * @returns {boolean} 是否超出限制
 */
function validatePromptLength (prompt, maxTokens = 4000) {
  // 保持向后兼容，但建议使用新的 token 工具
  const estimatedTokens = prompt.length / 3 // 粗略估算
  return estimatedTokens <= maxTokens
}

/**
 * 压缩提示词（当超出长度限制时）
 * @param {string} prompt - 原始提示词
 * @param {number} maxTokens - 最大 token 数
 * @param {string} model - 模型名称
 * @param {Object} config - 配置信息
 * @returns {Promise<string>} 压缩后的提示词
 */
async function compressPrompt (prompt, maxTokens = 4000, model = 'gpt-4', config = {}) {
  const currentTokens = await countTokens(prompt, model)

  if (currentTokens <= maxTokens) {
    return prompt
  }

  // 检查是否启用压缩功能
  const promptConfig = config.prompt || defaultConfig.prompt || {}

  // 配置类型验证
  let enableCompression = true // 默认启用
  if (typeof promptConfig.enableCompression === 'boolean') {
    enableCompression = promptConfig.enableCompression
  } else if (promptConfig.enableCompression !== undefined) {
    logger.warn(`配置项 prompt.enableCompression 应为布尔值，当前值: ${promptConfig.enableCompression}，使用默认值: true`)
  }

  if (!enableCompression) {
    logger.warn(`提示词长度超出限制 (${currentTokens}/${maxTokens} tokens)，但压缩功能已禁用`)
    logger.warn('这可能导致LLM调用失败，建议启用压缩或增加maxTokens配置')
    logger.info('要启用压缩，请在配置文件中设置 prompt.enableCompression: true')
    return prompt
  }

  logger.log(`    🔄 压缩提示词: ${currentTokens} -> ${maxTokens} tokens`)

  // 估算目标字符长度（基于当前 token/字符比例）
  const tokenToCharRatio = prompt.length / currentTokens
  const targetLength = Math.floor(maxTokens * tokenToCharRatio * 0.9) // 留10%缓冲

  // 压缩策略：保留核心信息，压缩详细内容
  let compressedPrompt = prompt

  // 1. 压缩代码差异部分（保留前 50% 的内容）
  const diffMatch = compressedPrompt.match(/(```diff[\s\S]*?```)/)
  if (diffMatch && diffMatch[1].length > 1000) {
    const diffContent = diffMatch[1]
    const lines = diffContent.split('\n')
    const keepLines = Math.floor(lines.length * 0.5)
    const compressedDiff = lines.slice(0, keepLines).join('\n') + '\n... (内容已压缩)'
    compressedPrompt = compressedPrompt.replace(diffMatch[1], compressedDiff)
  }

  // 2. 压缩文件详情（只保留重要文件）
  const fileDetailsRegex = /#### ([^\n]+)\n([\s\S]*?)(?=####|###|$)/g
  let match
  const importantFiles = []
  const lessImportantFiles = []

  while ((match = fileDetailsRegex.exec(compressedPrompt)) !== null) {
    const filePath = match[1]
    const content = match[0]

    // 判断文件重要性
    if (isImportantFile(filePath, {})) {
      importantFiles.push(content)
    } else {
      lessImportantFiles.push(content)
    }
  }

  // 如果仍然太长，只保留重要文件
  if (compressedPrompt.length > targetLength && lessImportantFiles.length > 0) {
    // 替换文件详情部分
    const fileDetailsSection = compressedPrompt.match(/(### 文件变更摘要[\s\S]*?)(?=###|$)/)
    if (fileDetailsSection) {
      const newFileDetails = `### 文件变更摘要\n${importantFiles.join('\n')}\n\n*注：已省略 ${lessImportantFiles.length} 个次要文件的详情*\n`
      compressedPrompt = compressedPrompt.replace(fileDetailsSection[1], newFileDetails)
    }
  }

  // 3. 如果还是太长，进一步压缩
  if (compressedPrompt.length > targetLength) {
    // 简单截断，但保留结尾的任务要求部分
    const taskSection = compressedPrompt.match(/(## 任务要求[\s\S]*)$/)
    if (taskSection) {
      const beforeTask = compressedPrompt.substring(0, compressedPrompt.indexOf(taskSection[1]))
      const truncateLength = targetLength - taskSection[1].length - 100 // 留一些缓冲
      const truncatedBefore = beforeTask.substring(0, truncateLength) + '\n\n*注：内容已压缩以适应长度限制*\n\n'
      compressedPrompt = truncatedBefore + taskSection[1]
    } else {
      compressedPrompt = compressedPrompt.substring(0, targetLength) + '\n\n*注：内容已截断*'
    }
  }

  const finalTokens = await countTokens(compressedPrompt, model)
  logger.log(`    ✅ 压缩完成: ${compressedPrompt.length} 字符 (${finalTokens} tokens)`)
  return compressedPrompt
}

/**
 * 智能检测项目类型
 * @param {Object} analysisData - 分析数据
 * @param {Object} config - 配置信息
 * @returns {string} 项目类型
 */
function detectProjectType (analysisData, config) {
  // 如果配置中明确指定了项目类型，直接使用
  if (config.project?.type && config.project.type !== 'auto') {
    return config.project.type
  }

  const fileTypes = analysisData.summary?.fileTypes || {}
  const packageChanges = analysisData.projectContext?.packageJsonChanges || []

  // 根据文件类型分布判断项目类型
  const totalFiles = Object.values(fileTypes).reduce((sum, count) => sum + count, 0)

  // Vue 项目判断
  if (fileTypes.vue && fileTypes.vue / totalFiles > 0.3) {
    return 'vue'
  }

  // React 项目判断
  if ((fileTypes.tsx || fileTypes.jsx) &&
    (fileTypes.tsx + fileTypes.jsx) / totalFiles > 0.2) {
    return 'react'
  }

  // 从依赖变更中推断项目类型
  const dependencyTypes = new Map()
  packageChanges.forEach(change => {
    if (change.name === 'next') dependencyTypes.set('nextjs', 3)
    if (change.name === 'nuxt') dependencyTypes.set('nuxt', 3)
    if (change.name === 'vue') dependencyTypes.set('vue', 2)
    if (change.name === 'react') dependencyTypes.set('react', 2)
    if (change.name === 'express') dependencyTypes.set('node', 2)
  })

  if (dependencyTypes.size > 0) {
    // 返回权重最高的项目类型
    return [...dependencyTypes.entries()].sort((a, b) => b[1] - a[1])[0][0]
  }

  // 默认根据主要文件类型判断
  if (fileTypes.vue) return 'vue'
  if (fileTypes.tsx || fileTypes.jsx) return 'react'
  if (fileTypes.js || fileTypes.ts) return 'javascript'

  return 'frontend'
}

/**
 * 检测技术栈
 * @param {Object} analysisData - 分析数据
 * @param {Object} config - 配置信息
 * @returns {string} 技术栈描述
 */
function detectTechStack (analysisData, _config) {
  const techStack = []
  const fileTypes = analysisData.summary?.fileTypes || {}

  // 框架检测
  if (fileTypes.vue) techStack.push('Vue.js')
  if (fileTypes.tsx || fileTypes.jsx) techStack.push('React')
  if (fileTypes.svelte) techStack.push('Svelte')

  // 语言检测
  if (fileTypes.ts || fileTypes.tsx) {
    techStack.push('TypeScript')
  } else if (fileTypes.js || fileTypes.jsx) {
    techStack.push('JavaScript')
  }

  // 样式检测
  if (fileTypes.scss || fileTypes.sass) {
    techStack.push('SCSS')
  } else if (fileTypes.less) {
    techStack.push('Less')
  } else if (fileTypes.css) {
    techStack.push('CSS')
  }

  // 从依赖变更中推断技术栈
  const packageChanges = analysisData.projectContext?.packageJsonChanges || []
  packageChanges.forEach(change => {
    if (change.name === 'next') techStack.push('Next.js')
    if (change.name === 'nuxt') techStack.push('Nuxt.js')
    if (change.name === 'express') techStack.push('Express')
  })

  // 去重并返回
  const uniqueTechStack = [...new Set(techStack)]
  return uniqueTechStack.length > 0 ? uniqueTechStack.join(', ') : '前端项目'
}

/**
 * 格式化文件类型分布
 * @param {Object} fileTypes - 文件类型统计
 * @returns {string} 格式化后的字符串
 */
function formatFileTypes (fileTypes) {
  const entries = Object.entries(fileTypes)
  if (entries.length === 0) {
    return '无'
  }

  return entries
    .sort(([, a], [, b]) => b - a) // 按数量排序
    .map(([ext, count]) => `${ext.replace('.', '')}: ${count}`)
    .join(', ')
}

/**
 * 注入文件详情信息
 * @param {string} prompt - 提示词
 * @param {Object} analysisData - 分析数据
 * @returns {string} 注入后的提示词
 */
function injectFileDetails (prompt, analysisData) {
  const filesData = analysisData.filesData || []

  if (filesData.length === 0) {
    return prompt.replace(/{# each parsed_files #}[\s\S]*?{\/each}/g, '暂无文件详情信息')
  }

  let fileDetailsSection = ''

  filesData.forEach(file => {
    // 跳过被忽略的文件
    if (!isImportantFile(file.filePath, {})) {
      return
    }

    // 构建导入模块列表
    const imports = file.structure?.imports || []
    const importModules = imports.map(imp => imp.module).join(', ') || '无'

    // 构建导出内容列表
    const exports = file.structure?.exports || []
    const exportNames = exports.map(exp => exp.name).join(', ') || '无'

    // 构建函数列表
    const functions = file.structure?.functions || []
    const functionNames = functions.map(func => func.name).join(', ') || '无'

    fileDetailsSection += `#### ${file.filePath}
- **变更状态**: ${file.status}
- **变更规模**: +${file.changeStats?.addedLines || 0} -${file.changeStats?.deletedLines || 0} ~${file.changeStats?.modifiedLines || 0}

**代码结构信息**:
- 导入模块: ${importModules}
- 导出内容: ${exportNames}
- 函数列表: ${functionNames}

`
  })

  // 替换模板中的文件详情部分
  return prompt.replace(/{# each parsed_files #}[\s\S]*?{\/each}/g, fileDetailsSection)
}

/**
 * 注入依赖和配置变更信息
 * @param {string} prompt - 提示词
 * @param {Object} analysisData - 分析数据
 * @returns {string} 注入后的提示词
 */
function injectDependencyChanges (prompt, analysisData) {
  const packageChanges = analysisData.projectContext?.packageJsonChanges || []
  const configChanges = analysisData.projectContext?.configFileChanges || []

  let dependencySection = ''

  if (packageChanges.length > 0) {
    dependencySection += '#### 包依赖变更\n'
    packageChanges.forEach(change => {
      const action = change.action === 'added'
        ? '新增'
        : change.action === 'removed'
          ? '移除'
          : change.action === 'updated' ? '更新' : change.action
      dependencySection += `- **${action}** \`${change.name}@${change.version || 'latest'}\`: ${change.description || '依赖包变更'}\n`
    })
    dependencySection += '\n'
  }

  if (configChanges.length > 0) {
    dependencySection += '#### 配置文件变更\n'
    configChanges.forEach(change => {
      dependencySection += `- **${change.file}**: ${change.changes || '配置文件有变更'}\n`
    })
    dependencySection += '\n'
  }

  if (dependencySection === '') {
    dependencySection = '暂无依赖和配置变更\n'
  }

  // 替换模板中的依赖变更部分
  return prompt.replace(/{# if has_dependency_changes #}[\s\S]*?{\/if}/g, dependencySection)
}

/**
 * 增强代码差异的上下文信息
 * @param {Object} file - 文件信息
 * @returns {string} 增强后的差异信息
 */
function enhanceDiffContext (file) {
  let contextInfo = ''

  // 添加文件类型和用途说明
  const fileType = getFileTypeDescription(file.filePath)
  if (fileType) {
    contextInfo += `## ${file.filePath} (${fileType})\n`
  } else {
    contextInfo += `## ${file.filePath}\n`
  }

  // 添加变更摘要
  const changeSummary = generateChangeSummary(file)
  if (changeSummary) {
    contextInfo += `**变更摘要**: ${changeSummary}\n`
  }

  // 添加影响的函数/组件信息
  const affectedComponents = extractAffectedComponents(file.diff)
  if (affectedComponents.length > 0) {
    contextInfo += `**影响组件**: ${affectedComponents.join(', ')}\n`
  }

  contextInfo += '\n' + file.diff

  return contextInfo
}

/**
 * 获取文件类型描述
 * @param {string} filePath - 文件路径
 * @returns {string} 文件类型描述
 */
function getFileTypeDescription (filePath) {
  if (filePath.endsWith('.vue')) return 'Vue组件'
  if (filePath.endsWith('.jsx') || filePath.endsWith('.tsx')) return 'React组件'
  if (filePath.includes('/components/')) return '组件文件'
  if (filePath.includes('/views/') || filePath.includes('/pages/')) return '页面文件'
  if (filePath.includes('/utils/') || filePath.includes('/helpers/')) return '工具函数'
  if (filePath.includes('/api/') || filePath.includes('/services/')) return 'API服务'
  if (filePath.includes('/store/') || filePath.includes('/stores/')) return '状态管理'
  if (filePath.includes('/router/')) return '路由配置'
  if (filePath.includes('/styles/') || filePath.endsWith('.css') || filePath.endsWith('.scss') || filePath.endsWith('.less')) return '样式文件'
  if (filePath.includes('package.json')) return '依赖配置'
  if (filePath.includes('.config.') || filePath.includes('config/')) return '配置文件'
  return ''
}

/**
 * 生成变更摘要
 * @param {Object} file - 文件信息
 * @returns {string} 变更摘要
 */
function generateChangeSummary (file) {
  const stats = file.changeStats || {}
  const added = stats.addedLines || 0
  const deleted = stats.deletedLines || 0
  const modified = stats.modifiedLines || 0

  if (added > deleted * 2) return '主要新增功能'
  if (deleted > added * 2) return '主要删除重构'
  if (modified > (added + deleted)) return '主要修改优化'
  if (added > 0 && deleted > 0) return '功能调整和优化'
  if (added > 0) return '新增功能'
  if (deleted > 0) return '删除功能'
  return '代码调整'
}

/**
 * 提取受影响的组件/函数名
 * @param {string} diff - 差异内容
 * @returns {Array} 受影响的组件/函数列表
 */
function extractAffectedComponents (diff) {
  const components = []
  const lines = diff.split('\n')

  lines.forEach(line => {
    // 提取函数名
    const functionMatch = line.match(/[+-]\s*(?:function\s+|const\s+|let\s+|var\s+)(\w+)/)
    if (functionMatch) {
      components.push(`函数:${functionMatch[1]}`)
    }

    // 提取Vue组件方法
    const vueMethodMatch = line.match(/[+-]\s*(\w+)\s*\(/)
    if (vueMethodMatch && !['if', 'for', 'while', 'switch'].includes(vueMethodMatch[1])) {
      components.push(`方法:${vueMethodMatch[1]}`)
    }

    // 提取CSS类名
    const cssClassMatch = line.match(/[+-]\s*\.([a-zA-Z-_]+)/)
    if (cssClassMatch) {
      components.push(`样式:${cssClassMatch[1]}`)
    }
  })

  // 去重并限制数量
  return [...new Set(components)].slice(0, 5)
}

/**
 * 注入代码差异信息
 * @param {string} prompt - 提示词
 * @param {Object} analysisData - 分析数据
 * @returns {string} 注入后的提示词
 */
function injectCodeDiffs (prompt, analysisData) {
  const filesData = analysisData.filesData || []

  if (filesData.length === 0) {
    return prompt.replace(/{filesData\.map\(file => file\.diff\)\.join\('\\n\\n'\)}/g, '暂无代码差异信息')
  }

  // 选择有diff的重要文件（限制数量以控制长度）
  const filesWithDiff = filesData
    .filter(file => file.diff && file.diff.trim() && isImportantFile(file.filePath, {}))
    .slice(0, 8) // 最多显示 8 个文件的 diff

  if (filesWithDiff.length === 0) {
    return prompt.replace(/{filesData\.map\(file => file\.diff\)\.join\('\\n\\n'\)}/g, '暂无有效的代码差异信息')
  }

  // 使用增强的上下文信息
  const diffContent = filesWithDiff
    .map(file => enhanceDiffContext(file))
    .join('\n\n')

  // 替换模板中的代码差异部分
  return prompt.replace(/{filesData\.map\(file => file\.diff\)\.join\('\\n\\n'\)}/g, diffContent)
}

/**
 * 清理提示词中的空字段
 * @param {string} prompt - 原始提示词
 * @returns {string} 清理后的提示词
 */
function cleanEmptyFields (prompt) {
  return prompt
    // 移除显示为"无"的字段行（精确匹配，避免误删"无障碍"等词汇）
    .replace(/^- \*\*[^*]+\*\*: 无$/gm, '')
    .replace(/^- [^:]+: 无$/gm, '')
    // 移除只有标题没有内容的代码结构信息块
    .replace(/\*\*代码结构信息\*\*:\n+(?=####|\n###|\n##|$)/g, '')
    // 移除空的代码结构信息块（只有标题的情况）
    .replace(/\*\*代码结构信息\*\*:\s*\n\n/g, '')
    // 清理连续的空行，最多保留两个换行符
    .replace(/\n{3,}/g, '\n\n')
    // 清理行尾的空白字符
    .replace(/[ \t]+$/gm, '')
    // 清理文件详情末尾多余的空行
    .replace(/(#### [^\n]+\n- \*\*变更状态\*\*: [^\n]+\n- \*\*变更规模\*\*: [^\n]+\n)\n+(?=####)/g, '$1\n')
}

/**
 * 判断文件是否重要
 * @param {string} filePath - 文件路径
 * @param {Object} config - 配置信息
 * @returns {boolean} 是否重要
 */
function isImportantFile (filePath, config = {}) {
  const analysisConfig = config.analysis || defaultConfig.analysis
  const fileImportanceConfig = analysisConfig.fileImportance || {}

  // 检查是否在全局忽略列表中
  const ignorePatterns = analysisConfig.ignorePatterns || []
  const fileName = filePath.split('/').pop() // 获取文件名
  if (ignorePatterns.includes(fileName)) {
    return false
  }

  const importantPatterns = fileImportanceConfig.importantPatterns || []
  const unimportantPatterns = fileImportanceConfig.unimportantPatterns || []

  // 先检查是否为不重要文件
  if (unimportantPatterns.some(pattern => pattern.test(filePath))) {
    return false
  }

  // 再检查是否为重要文件
  return importantPatterns.some(pattern => pattern.test(filePath))
}

/**
 * 验证提示词相关配置
 * @param {Object} config - 配置对象
 * @returns {Object} 验证结果 { isValid: boolean, errors: string[] }
 */
function validatePromptConfig (config = {}) {
  const errors = []
  const promptConfig = config.prompt || {}

  // 验证 enableCompression 配置
  if (promptConfig.enableCompression !== undefined && typeof promptConfig.enableCompression !== 'boolean') {
    errors.push(`prompt.enableCompression 应为布尔值，当前类型: ${typeof promptConfig.enableCompression}`)
  }

  // 验证 compressionThreshold 配置
  if (promptConfig.compressionThreshold !== undefined) {
    if (typeof promptConfig.compressionThreshold !== 'number') {
      errors.push(`prompt.compressionThreshold 应为数字，当前类型: ${typeof promptConfig.compressionThreshold}`)
    } else if (promptConfig.compressionThreshold < 0 || promptConfig.compressionThreshold > 1) {
      errors.push(`prompt.compressionThreshold 应在 0-1 之间，当前值: ${promptConfig.compressionThreshold}`)
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export {
  buildEnhancedPrompt,
  loadPromptTemplate,
  injectContextData,
  adjustPromptByProjectType,
  validatePromptLength,
  compressPrompt,
  detectProjectType,
  detectTechStack,
  validatePromptConfig
}
