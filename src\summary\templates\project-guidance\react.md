## React 项目特定分析指导

在分析 React 项目时，请特别关注：

### 组件分析
- **组件类型**: 区分函数组件、类组件、高阶组件
- **Hooks 使用**: 关注 useState、useEffect、useContext 等 Hooks 的使用
- **组件层级**: 分析组件的父子关系和数据流向
- **性能优化**: 关注 React.memo、useMemo、useCallback 的使用

### 状态管理
- **本地状态**: useState、useReducer 的使用情况
- **全局状态**: Redux、Zustand、Context API 的变更
- **状态提升**: 组件间状态共享的实现方式

### 路由和导航
- **React Router**: 路由配置和页面跳转逻辑
- **动态路由**: 参数传递和路由守卫
- **代码分割**: React.lazy 和 Suspense 的使用

### 样式和UI
- **CSS-in-JS**: styled-components、emotion 等的使用
- **UI 组件库**: Ant Design、Material-UI 等的集成
- **响应式设计**: 移动端适配和断点处理