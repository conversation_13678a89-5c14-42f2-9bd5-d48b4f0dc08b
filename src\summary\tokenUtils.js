/**
 * Token 计算工具模块
 * 使用 tiktoken 库准确计算文本的 token 数量
 * 合并了 tokenCounter.js 和 tokenUtils.js 的功能
 */

import tiktoken from 'tiktoken'
import { logger } from './logger.js'

/**
 * 模型到编码的映射配置
 */
const MODEL_ENCODINGS = {
  'gpt-4': 'cl100k_base',
  'gpt-4-turbo': 'cl100k_base',
  'gpt-4-32k': 'cl100k_base',
  'gpt-3.5-turbo': 'cl100k_base',
  'gpt-3.5-turbo-16k': 'cl100k_base',
  'text-davinci-003': 'p50k_base',
  'text-davinci-002': 'p50k_base',
  'code-davinci-002': 'p50k_base'
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
  model: 'gpt-4',
  encoding: 'cl100k_base',
  enableCache: true,
  cacheSize: 1000
}

/**
 * Token 计数器类
 */
class TokenCounter {
  constructor (config = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.cache = new Map()
    this.stats = {
      totalCalculations: 0,
      cacheHits: 0,
      cacheMisses: 0
    }
  }

  /**
   * 获取模型对应的编码方式
   * @param {string} model - 模型名称
   * @returns {string} 编码方式
   */
  getEncodingForModel (model = this.config.model) {
    return MODEL_ENCODINGS[model] || this.config.encoding
  }

  /**
   * 生成缓存键
   * @param {string} text - 文本内容
   * @param {string} model - 模型名称
   * @returns {string} 缓存键
   */
  generateCacheKey (text, model) {
    // 使用文本长度和前后部分内容生成简单的缓存键
    const textHash = text.length + '_' +
      text.substring(0, 50) + '_' +
      text.substring(text.length - 50)
    return `${model}_${textHash}`
  }

  /**
   * 从缓存获取结果
   * @param {string} key - 缓存键
   * @returns {number|null} 缓存的 token 数量
   */
  getFromCache (key) {
    if (!this.config.enableCache) return null

    if (this.cache.has(key)) {
      this.stats.cacheHits++
      return this.cache.get(key)
    }

    this.stats.cacheMisses++
    return null
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {number} value - token 数量
   */
  setCache (key, value) {
    if (!this.config.enableCache) return

    // 简单的 LRU 实现：当缓存满时删除最旧的条目
    if (this.cache.size >= this.config.cacheSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    this.cache.set(key, value)
  }

  /**
   * 估算 token 数量（备用方法）
   * @param {string} text - 文本内容
   * @returns {number} 估算的 token 数量
   */
  estimateTokens (text) {
    if (!text || typeof text !== 'string') return 0

    // 改进的估算算法
    // 中文字符通常占用更多 token，英文单词平均约 1.3 token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').split(/\s+/).filter(word => word.length > 0).length
    const otherChars = text.length - chineseChars - text.replace(/[\u4e00-\u9fff]/g, '').replace(/\s/g, '').length

    return Math.ceil(chineseChars * 1.5 + englishWords * 1.3 + otherChars * 0.5)
  }

  /**
   * 验证输入参数
   * @param {string} text - 文本内容
   * @param {string} model - 模型名称
   * @throws {Error} 输入无效时抛出错误
   */
  validateInput (text, model) {
    if (typeof text !== 'string') {
      throw new Error('文本必须是字符串类型')
    }

    if (model && typeof model !== 'string') {
      throw new Error('模型名称必须是字符串类型')
    }

    if (model && !MODEL_ENCODINGS[model] && model !== this.config.model) {
      logger.warn(`未知的模型类型: ${model}，将使用默认编码`)
    }
  }  /*
*
   * 计算单个文本的 token 数量
   * @param {string} text - 文本内容
   * @param {string} model - 模型名称（可选）
   * @returns {Promise<Object>} token 计算结果
   */
  async countTokens (text, model = this.config.model) {
    try {
      this.validateInput(text, model)
      this.stats.totalCalculations++

      if (!text) {
        return {
          count: 0,
          model,
          encoding: this.getEncodingForModel(model),
          method: 'direct'
        }
      }

      // 检查缓存
      const cacheKey = this.generateCacheKey(text, model)
      const cachedResult = this.getFromCache(cacheKey)
      if (cachedResult !== null) {
        return {
          count: cachedResult,
          model,
          encoding: this.getEncodingForModel(model),
          method: 'cached'
        }
      }

      let tokenCount
      let method

      try {
        const encoding = tiktoken.get_encoding(this.getEncodingForModel(model))
        const tokens = encoding.encode(text)
        tokenCount = tokens.length
        method = 'tiktoken'
        encoding.free() // 释放编码器资源
      } catch (error) {
        logger.warn('tiktoken 计算失败，使用估算方法:', error.message)
        tokenCount = this.estimateTokens(text)
        method = 'estimated_fallback'
      }

      // 设置缓存
      this.setCache(cacheKey, tokenCount)

      return {
        count: tokenCount,
        model,
        encoding: this.getEncodingForModel(model),
        method,
        metadata: {
          textLength: text.length,
          avgTokenLength: text.length / tokenCount
        }
      }

    } catch (error) {
      logger.error('Token 计算失败:', error)
      throw new Error(`Token 计算失败: ${error.message}`)
    }
  }

  /**
   * 批量计算多个文本的 token 数量
   * @param {Array<string>} texts - 文本数组
   * @param {string} model - 模型名称（可选）
   * @returns {Promise<Array<Object>>} token 计算结果数组
   */
  async countTokensBatch (texts, model = this.config.model) {
    if (!Array.isArray(texts)) {
      throw new Error('texts 必须是数组类型')
    }

    const results = []
    for (const text of texts) {
      try {
        const result = await this.countTokens(text, model)
        results.push(result)
      } catch (error) {
        logger.error('批量计算中的文本处理失败:', error)
        results.push({
          count: 0,
          model,
          encoding: this.getEncodingForModel(model),
          method: 'error',
          error: error.message
        })
      }
    }

    return results
  }

  /**
   * 获取详细的 token 统计信息
   * @param {string} text - 文本内容
   * @param {string} model - 模型名称（可选）
   * @returns {Promise<Object>} 详细统计信息
   */
  async getTokenStats (text, model = this.config.model) {
    const result = await this.countTokens(text, model)

    // 按段落分析
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim())
    const paragraphStats = []

    for (let i = 0; i < paragraphs.length; i++) {
      const paragraphResult = await this.countTokens(paragraphs[i], model)
      paragraphStats.push({
        index: i,
        tokens: paragraphResult.count,
        length: paragraphs[i].length,
        percentage: (paragraphResult.count / result.count * 100).toFixed(2)
      })
    }

    return {
      totalTokens: result.count,
      totalLength: text.length,
      model: result.model,
      encoding: result.encoding,
      method: result.method,
      paragraphs: paragraphStats,
      averageTokensPerParagraph: paragraphStats.length > 0
        ? (result.count / paragraphStats.length).toFixed(2)
        : 0,
      tokensPerCharacter: (result.count / text.length).toFixed(4)
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats () {
    return {
      ...this.stats,
      cacheSize: this.cache.size,
      cacheHitRate: this.stats.totalCalculations > 0
        ? (this.stats.cacheHits / this.stats.totalCalculations * 100).toFixed(2) + '%'
        : '0%'
    }
  }

  /**
   * 清空缓存
   */
  clearCache () {
    this.cache.clear()
    logger.log('Token 计数器缓存已清空')
  }
}

// 创建默认实例
const defaultTokenCounter = new TokenCounter()

// 简化的工具函数接口
/**
 * 计算文本的 token 数量（简化接口）
 * @param {string} text - 文本内容
 * @param {string} model - 模型名称（可选）
 * @returns {Promise<number>} token 数量
 */
async function countTokens (text, model) {
  try {
    const result = await defaultTokenCounter.countTokens(text, model)
    return result.count
  } catch (error) {
    logger.error('Token 计算失败，使用估算方法:', error)
    return defaultTokenCounter.estimateTokens(text)
  }
}

/**
 * 同步计算文本的 token 数量（使用估算方法）
 * @param {string} text - 文本内容
 * @returns {number} 估算的 token 数量
 */
function estimateTokens (text) {
  return defaultTokenCounter.estimateTokens(text)
}

/**
 * 检查文本是否超过指定的 token 限制
 * @param {string} text - 文本内容
 * @param {number} maxTokens - 最大 token 数量
 * @param {string} model - 模型名称（可选）
 * @returns {Promise<Object>} 检查结果
 */
async function validateTokenLength (text, maxTokens, model) {
  const tokenCount = await countTokens(text, model)

  return {
    tokenCount,
    maxTokens,
    isValid: tokenCount <= maxTokens,
    excess: Math.max(0, tokenCount - maxTokens),
    percentage: (tokenCount / maxTokens * 100).toFixed(2)
  }
}

/**
 * 获取 token 计算的详细信息
 * @param {string} text - 文本内容
 * @param {string} model - 模型名称（可选）
 * @returns {Promise<Object>} 详细信息
 */
async function getTokenInfo (text, model) {
  try {
    return await defaultTokenCounter.countTokens(text, model)
  } catch (error) {
    logger.error('获取 token 信息失败:', error)
    return {
      count: estimateTokens(text),
      model: model || 'gpt-4',
      encoding: 'unknown',
      method: 'estimated_error',
      error: error.message
    }
  }
}

/**
 * 批量计算多个文本的 token 数量
 * @param {Array<string>} texts - 文本数组
 * @param {string} model - 模型名称（可选）
 * @returns {Promise<Array<number>>} token 数量数组
 */
async function countTokensBatch (texts, model) {
  try {
    const results = await defaultTokenCounter.countTokensBatch(texts, model)
    return results.map(result => result.count)
  } catch (error) {
    logger.error('批量 token 计算失败:', error)
    return texts.map(text => estimateTokens(text))
  }
}

/**
 * 获取缓存统计信息
 * @returns {Object} 缓存统计
 */
function getCacheStats () {
  return defaultTokenCounter.getCacheStats()
}

/**
 * 清空 token 计算缓存
 */
function clearTokenCache () {
  defaultTokenCounter.clearCache()
}

export {
  TokenCounter,
  defaultTokenCounter,
  MODEL_ENCODINGS,
  countTokens,
  estimateTokens,
  validateTokenLength,
  getTokenInfo,
  countTokensBatch,
  getCacheStats,
  clearTokenCache
}
