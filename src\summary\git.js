/**
 * Git 操作工具
 * 处理 git 操作，获取增强版 diff 与文件内容
 */

import { execSync } from 'child_process'
import fs from 'fs'
import { logger } from './logger.js'

/**
 * 执行 git 命令
 * @param {string} command - git 命令
 * @param {string} cwd - 工作目录
 * @returns {string} 命令输出
 */
function execGitCommand (command, cwd) {
  try {
    const result = execSync(command, {
      cwd,
      encoding: 'utf8',
      maxBuffer: 10 * 1024 * 1024 // 10MB buffer
    })
    return result.trim()
  } catch (error) {
    throw new Error(`Git 命令执行失败: ${command}\n错误: ${error.message}`)
  }
}

/**
 * 验证是否为有效的 Git 仓库
 * @param {string} repoPath - 仓库路径
 */
function validateGitRepository (repoPath) {
  if (!fs.existsSync(repoPath)) {
    throw new Error(`仓库路径不存在: ${repoPath}`)
  }

  try {
    execGitCommand('git rev-parse --git-dir', repoPath)
  } catch (error) {
    throw new Error(`不是有效的 Git 仓库: ${repoPath}`)
  }
}

/**
 * 验证 commit 是否存在
 * @param {string} repoPath - 仓库路径
 * @param {string} commitId - commit ID
 */
function validateCommit (repoPath, commitId) {
  try {
    execGitCommand(`git rev-parse --verify ${commitId}`, repoPath)
  } catch (error) {
    throw new Error(`无效的 commit ID: ${commitId}`)
  }
}

/**
 * 解析 diff 统计信息
 * @param {string} diffStat - git diff --stat 输出
 * @returns {Object} 统计信息
 */
function parseDiffStats (diffStat) {
  const lines = diffStat.split('\n').filter(line => line.trim())
  const stats = {
    addedLines: 0,
    deletedLines: 0,
    modifiedLines: 0
  }

  for (const line of lines) {
    const match = line.match(/(\d+) insertions?\(\+\)|(\d+) deletions?\(-\)/g)
    if (match) {
      for (const m of match) {
        if (m.includes('insertion')) {
          stats.addedLines += parseInt(m.match(/(\d+)/)[1])
        } else if (m.includes('deletion')) {
          stats.deletedLines += parseInt(m.match(/(\d+)/)[1])
        }
      }
    }
  }

  stats.modifiedLines = Math.min(stats.addedLines, stats.deletedLines)
  return stats
}

/**
 * 获取增强版 diff 信息
 * @param {string} repoPath - 仓库路径
 * @param {string} startCommit - 起始 commit
 * @param {string} endCommit - 结束 commit
 * @returns {Array} 增强版 diff 数据
 */
async function getEnhancedDiff (repoPath, startCommit, endCommit) {
  logger.log(`📂 正在分析仓库: ${repoPath}`)
  logger.log(`🔄 对比范围: ${startCommit} -> ${endCommit}`)

  // 验证仓库和 commit
  validateGitRepository(repoPath)
  validateCommit(repoPath, startCommit)
  validateCommit(repoPath, endCommit)

  // 获取变更文件列表
  const diffNameStatus = execGitCommand(
    `git diff --name-status ${startCommit} ${endCommit}`,
    repoPath
  )

  if (!diffNameStatus) {
    logger.log('📝 没有发现文件变更')
    return []
  }

  const files = diffNameStatus.split('\n').filter(line => line.trim())
  const enhancedDiffs = []

  logger.log(`📊 发现 ${files.length} 个变更文件`)

  for (const file of files) {
    const [status, ...pathParts] = file.split('\t')
    const filePath = pathParts.join('\t') // 处理文件名中可能包含的制表符

    logger.log(`  📄 处理文件: ${filePath} (${status})`)

    try {
      // 获取文件的 diff 内容
      const diff = execGitCommand(
        `git diff ${startCommit} ${endCommit} -- "${filePath}"`,
        repoPath
      )

      // 获取文件的统计信息
      const diffStat = execGitCommand(
        `git diff --stat ${startCommit} ${endCommit} -- "${filePath}"`,
        repoPath
      )

      const changeStats = parseDiffStats(diffStat)

      // 获取变更前后的文件内容
      let beforeContent = ''
      let afterContent = ''

      if (status !== 'A') { // 不是新增文件
        try {
          beforeContent = await getFileAtCommit(repoPath, startCommit, filePath)
        } catch (error) {
          logger.warn(`无法获取变更前内容: ${error.message}`)
        }
      }

      if (status !== 'D') { // 不是删除文件
        try {
          afterContent = await getFileAtCommit(repoPath, endCommit, filePath)
        } catch (error) {
          logger.warn(`无法获取变更后内容: ${error.message}`)
        }
      }

      // 转换状态标识
      const statusMap = {
        A: 'added',
        M: 'modified',
        D: 'deleted',
        R: 'renamed',
        C: 'copied'
      }

      enhancedDiffs.push({
        filePath,
        status: statusMap[status[0]] || 'modified',
        diff,
        changeStats,
        beforeContent,
        afterContent
      })

      logger.log(`    ✅ 完成: +${changeStats.addedLines} -${changeStats.deletedLines} ~${changeStats.modifiedLines}`)

    } catch (error) {
      logger.error(`处理文件失败: ${error.message}`)
    }
  }

  logger.log(`✅ 增强版 diff 分析完成，共处理 ${enhancedDiffs.length} 个文件`)
  return enhancedDiffs
}

/**
 * 获取特定 commit 的文件内容
 * @param {string} repoPath - 仓库路径
 * @param {string} commitId - commit ID
 * @param {string} filePath - 文件路径
 * @returns {string} 文件内容
 */
async function getFileAtCommit (repoPath, commitId, filePath) {
  try {
    const content = execGitCommand(
      `git show ${commitId}:"${filePath}"`,
      repoPath
    )
    return content
  } catch (error) {
    // 文件可能在该 commit 中不存在
    if (error.message.includes('does not exist') || error.message.includes('exists on disk')) {
      return ''
    }
    throw new Error(`获取文件内容失败 (${commitId}:${filePath}): ${error.message}`)
  }
}

/**
 * 获取 commit 信息
 * @param {string} repoPath - 仓库路径
 * @param {string} startCommit - 起始 commit
 * @param {string} endCommit - 结束 commit
 * @returns {Object} commit 信息
 */
async function getCommitInfo (repoPath, startCommit, endCommit) {
  logger.log('📋 正在获取 commit 信息...')

  try {
    // 获取 commit 数量
    const commitCount = execGitCommand(
      `git rev-list --count ${startCommit}..${endCommit}`,
      repoPath
    )

    // 获取作者列表
    const authorsOutput = execGitCommand(
      `git log --format="%an" ${startCommit}..${endCommit}`,
      repoPath
    )

    const authors = [...new Set(authorsOutput.split('\n').filter(author => author.trim()))]

    // 获取时间范围
    let timeSpan = ''
    try {
      const startDate = execGitCommand(
        `git show -s --format="%ci" ${startCommit}`,
        repoPath
      ).split(' ')[0]

      const endDate = execGitCommand(
        `git show -s --format="%ci" ${endCommit}`,
        repoPath
      ).split(' ')[0]

      timeSpan = `${startDate} to ${endDate}`
    } catch (error) {
      logger.warn(`获取时间范围失败: ${error.message}`)
      timeSpan = 'Unknown'
    }

    // 获取分支信息
    let branchInfo = ''
    try {
      const currentBranch = execGitCommand('git branch --show-current', repoPath)
      branchInfo = `${startCommit.substring(0, 7)} -> ${endCommit.substring(0, 7)} (${currentBranch})`
    } catch (error) {
      logger.warn(`获取分支信息失败: ${error.message}`)
      branchInfo = `${startCommit.substring(0, 7)} -> ${endCommit.substring(0, 7)}`
    }

    const commitInfo = {
      commitCount: parseInt(commitCount) || 0,
      authors,
      timeSpan,
      branchInfo
    }

    logger.log('📊 Commit 信息:')
    logger.log(`  📈 提交数量: ${commitInfo.commitCount}`)
    logger.log(`  👥 参与作者: ${commitInfo.authors.join(', ')}`)
    logger.log(`  📅 时间范围: ${commitInfo.timeSpan}`)
    logger.log(`  🌿 分支信息: ${commitInfo.branchInfo}`)

    return commitInfo

  } catch (error) {
    logger.error(`获取 commit 信息失败: ${error.message}`)
    return {
      commitCount: 0,
      authors: [],
      timeSpan: 'Unknown',
      branchInfo: `${startCommit.substring(0, 7)} -> ${endCommit.substring(0, 7)}`
    }
  }
}

export {
  getEnhancedDiff,
  getFileAtCommit,
  getCommitInfo
}
