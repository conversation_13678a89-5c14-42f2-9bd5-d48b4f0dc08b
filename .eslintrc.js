module.exports = {
  env: {
    browser: false,
    es2021: true,
    node: true
  },
  extends: [
    'standard'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    // 允许 console.log 用于日志输出
    'no-console': 'off',

    // 不允许未使用的变量
    'no-unused-vars': 'error',

    // 允许空的 catch 块（有时需要忽略错误）
    'no-empty': ['error', { allowEmptyCatch: true }],

    // 要求使用 === 和 !==
    'eqeqeq': 'error',

    // 禁止使用 var
    'no-var': 'error',

    // 优先使用 const
    'prefer-const': 'error',

    // 要求箭头函数的参数使用圆括号
    'arrow-parens': ['error', 'as-needed'],

    // 要求对象字面量属性名称使用引号
    'quote-props': ['error', 'as-needed'],

    // 不使用分号
    'semi': ['error', 'never'],

    // 不要求尾随逗号
    'comma-dangle': 'off',

    // 要求空行
    'padded-blocks': 'off',

    // 不允许多个空行
    'no-multiple-empty-lines': ['error', { max: 1, maxEOF: 1 }]
  },
  ignorePatterns: [
    'node_modules/',
    'dist/',
    'build/',
    'coverage/',
    'logs/',
    'summary/',
    '*.min.js'
  ]
}
