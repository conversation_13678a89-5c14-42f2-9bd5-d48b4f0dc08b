/**
 * 文件名生成工具函数
 * 提供统一的文件名生成和清理功能
 */

import path from 'path'

/**
 * 从仓库路径中提取项目名称
 * @param {string} repoPath - 仓库路径
 * @returns {string} 清理后的项目名称
 */
function extractProjectName (repoPath) {
  if (!repoPath || typeof repoPath !== 'string') {
    return 'unknown-project'
  }

  // 获取目录名
  const projectName = path.basename(repoPath)

  // 如果项目名为空或只是点号，使用默认名称
  if (!projectName || projectName === '.' || projectName === '..') {
    return 'unnamed-project'
  }

  // 清理特殊字符，确保文件系统安全
  return sanitizeFileName(projectName)
}

/**
 * 清理文件名中的特殊字符
 * @param {string} fileName - 原始文件名
 * @returns {string} 清理后的文件名
 */
function sanitizeFileName (fileName) {
  if (!fileName || typeof fileName !== 'string') {
    return 'unnamed'
  }

  return fileName
      .replace(/[<>:"/\\|?*]/g, '-') // 替换文件系统不安全字符为连字符
      .replace(/\s+/g, '-') // 替换空格为连字符
      .replace(/-+/g, '-') // 合并多个连字符
      .replace(/^-|-$/g, '') // 移除开头和结尾的连字符
      .substring(0, 50) || // 限制长度，避免文件名过长
    'unnamed' // 如果清理后为空，使用默认名称
}

/**
 * 生成统一格式的文件名
 * @param {string} repoPath - 仓库路径
 * @param {string} startCommit - 起始提交ID
 * @param {string} endCommit - 结束提交ID
 * @param {string} fileType - 文件类型 ('log' 或 'md')
 * @returns {string} 格式化的文件名
 */
function generateFileName (repoPath, startCommit, endCommit, fileType) {
  // 1. 提取项目名称
  const projectName = extractProjectName(repoPath)

  // 2. 生成时间戳
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')

  // 3. 生成短提交ID
  const shortStart = startCommit ? startCommit.substring(0, 7) : 'unknown'
  const shortEnd = endCommit ? endCommit.substring(0, 7) : 'unknown'

  // 4. 组合文件名
  return `${projectName}-${timestamp}-${shortStart}-${shortEnd}.${fileType}`
}

export {
  extractProjectName,
  sanitizeFileName,
  generateFileName
}
