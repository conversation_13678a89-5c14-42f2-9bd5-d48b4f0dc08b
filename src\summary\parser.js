/**
 * AST 解析工具
 * 使用轻量级解析提取文件结构和语义信息，专注于前端项目
 */

import path from 'path'
import { logger } from './logger.js'

/**
 * 解析文件结构，提取语义信息
 * @param {Array} enhancedDiffs - 增强版 diff 数据
 * @returns {Array} 解析后的文件结构信息
 */
async function parseFilesStructure (enhancedDiffs) {
  logger.log('🔍 开始解析文件结构...')

  const parsedFiles = []
  let processedCount = 0

  for (const diffFile of enhancedDiffs) {
    try {
      logger.log(`  📄 解析文件: ${diffFile.filePath}`)

      // 检测语言
      const language = detectLanguage(diffFile.filePath)

      // 只解析支持的前端文件类型
      if (!isSupportedFileType(diffFile.filePath)) {
        logger.log(`    ⏭️  跳过不支持的文件类型: ${language}`)
        continue
      }

      // 解析文件内容（使用变更后的内容）
      const content = diffFile.afterContent || ''
      const structure = await parseFileContent(content, diffFile.filePath)

      // 检测框架
      const framework = detectFramework(structure, diffFile.filePath)

      // 分析文件角色
      const role = analyzeFileRole(diffFile.filePath, structure, framework)

      const parsedFile = {
        filePath: diffFile.filePath,
        role,
        language,
        framework,
        structure
      }

      parsedFiles.push(parsedFile)
      processedCount++

      logger.log(`    ✅ 解析完成: ${role || 'Unknown'} (${language}/${framework})`)

    } catch (error) {
      logger.warn(`解析文件失败 (${diffFile.filePath}): ${error.message}`)
    }
  }

  logger.log(`✅ 文件结构解析完成，共处理 ${processedCount} 个文件`)
  return parsedFiles
}

/**
 * 分析文件角色（宽松模式）
 * @param {string} filePath - 文件路径
 * @param {Object} structure - 文件结构信息
 * @param {string} framework - 检测到的框架
 * @returns {string|null} 文件角色
 */
function analyzeFileRole (filePath, structure, framework) {
  // 优先通过明确的路径模式判断
  const clearPatterns = {
    Page: ['/pages/', '/views/', '/screens/', '/routes/'],
    Component: ['/components/', '/widgets/', '/ui/'],
    Hook: ['/hooks/', '/composables/', '/use/'],
    Store: ['/stores/', '/pinia/', '/redux/', '/zustand/', '/store/'],
    API: ['/api/', '/services/', '/endpoints/'],
    Util: ['/utils/', '/helpers/', '/lib/'],
    Config: ['/config/', '/settings/', '.config.', '.env'],
    Test: ['/test/', '/tests/', '/__tests__/', '.test.', '.spec.'],
    Middleware: ['/middleware/', '/middlewares/'],
    Layout: ['/layouts/', '/layout/']
  }

  // 如果路径匹配明确模式，直接返回
  for (const [role, patterns] of Object.entries(clearPatterns)) {
    if (patterns.some(pattern => filePath.includes(pattern))) {
      return role
    }
  }

  // 基于文件扩展名判断
  const ext = path.extname(filePath).toLowerCase()
  const extensionMapping = {
    '.vue': 'Component',
    '.jsx': 'Component',
    '.tsx': 'Component',
    '.svelte': 'Component'
  }

  if (extensionMapping[ext]) {
    return extensionMapping[ext]
  }

  // 如果路径不明确，尝试基于文件名和结构特征猜测
  const fileName = path.basename(filePath, path.extname(filePath))

  // Hook 文件名模式
  if (/^use[A-Z]/.test(fileName)) {
    return 'Hook'
  }

  // 测试文件
  if (/\.(test|spec|e2e)$/.test(fileName)) {
    return 'Test'
  }

  // 配置文件
  if (/^(config|settings|env)/.test(fileName)) {
    return 'Config'
  }

  // 基于文件结构特征判断
  if (structure) {
    // 检查是否为 Store
    const storePatterns = ['defineStore', 'createStore', 'configureStore', 'createSlice']
    const hasStorePattern = structure.functions?.some(func =>
      storePatterns.some(pattern => func.name.includes(pattern))
    ) || structure.imports?.some(imp =>
      storePatterns.some(pattern => imp.items.includes(pattern))
    )

    if (hasStorePattern) {
      return 'Store'
    }

    // 检查是否为组件
    if (structure.exports && structure.exports.length > 0) {
      const hasDefaultExport = structure.exports.some(exp => exp.type === 'default')
      const hasReactPatterns = structure.imports && structure.imports.some(imp =>
        imp.module === 'react' || imp.module.includes('react')
      )

      // React 组件
      if (hasDefaultExport && hasReactPatterns) {
        return 'Component'
      }

      // Vue 组件
      if (framework === 'vue' && hasDefaultExport) {
        return 'Component'
      }

      // 有组件相关的结构
      if (structure.components && structure.components.length > 0) {
        return 'Component'
      }
    }

    // 检查是否为 API 文件
    const apiPatterns = ['fetch', 'axios', 'request', 'api', 'service']
    const hasApiPattern = structure.functions?.some(func =>
      apiPatterns.some(pattern => func.name.toLowerCase().includes(pattern))
    ) || structure.imports?.some(imp =>
      apiPatterns.some(pattern => imp.module.includes(pattern))
    )

    if (hasApiPattern) {
      return 'API'
    }

    // 检查是否为工具文件
    const utilPatterns = ['helper', 'util', 'tool', 'common']
    const hasUtilPattern = utilPatterns.some(pattern =>
      fileName.toLowerCase().includes(pattern)
    )

    if (hasUtilPattern) {
      return 'Util'
    }
  }

  // 特殊文件名判断
  if (fileName.toLowerCase().includes('middleware')) {
    return 'Middleware'
  }

  if (fileName.toLowerCase().includes('layout')) {
    return 'Layout'
  }

  // 无法确定时返回 null，交由 AI 判断
  return null
}

/**
 * 检测编程语言
 * @param {string} filePath - 文件路径
 * @returns {string} 编程语言
 */
function detectLanguage (filePath) {
  const ext = path.extname(filePath).toLowerCase()
  const languageMap = {
    '.js': 'javascript',
    '.jsx': 'javascript',
    '.ts': 'typescript',
    '.tsx': 'typescript',
    '.vue': 'vue',
    '.py': 'python',
    '.java': 'java',
    '.go': 'go',
    '.rs': 'rust',
    '.php': 'php',
    '.rb': 'ruby',
    '.cs': 'csharp',
    '.cpp': 'cpp',
    '.c': 'c',
    '.h': 'c',
    '.hpp': 'cpp'
  }

  return languageMap[ext] || 'unknown'
}

/**
 * 解析文件内容，提取结构信息
 * @param {string} content - 文件内容
 * @param {string} filePath - 文件路径
 * @returns {Object} 文件结构信息
 */
async function parseFileContent (content, filePath) {
  const structure = {
    imports: [],
    exports: [],
    functions: [],
    classes: [],
    hooks: [],
    components: []
  }

  if (!content || content.trim() === '') {
    return structure
  }

  const language = detectLanguage(filePath)

  // 根据语言类型选择解析策略
  switch (language) {
    case 'javascript':
    case 'typescript':
      return parseJavaScriptContent(content, filePath)
    case 'vue':
      return parseVueContent(content, filePath)
    case 'json':
      return parseJsonContent(content, filePath)
    default:
      return parseGenericContent(content, filePath)
  }
}

/**
 * 解析 JavaScript/TypeScript 文件内容
 * @param {string} content - 文件内容
 * @param {string} filePath - 文件路径
 * @returns {Object} 解析结果
 */
function parseJavaScriptContent (content, _filePath) {
  const structure = {
    imports: parseImports(content),
    exports: parseExports(content),
    functions: parseFunctions(content),
    classes: parseClasses(content),
    hooks: parseHooks(content),
    components: parseComponents(content)
  }

  return structure
}

/**
 * 解析 Vue 文件内容
 * @param {string} content - 文件内容
 * @param {string} filePath - 文件路径
 * @returns {Object} 解析结果
 */
function parseVueContent (content, filePath) {
  // 提取 <script> 标签内容
  const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/i)
  const scriptContent = scriptMatch ? scriptMatch[1] : ''

  // 提取 <template> 标签内容
  const templateMatch = content.match(/<template[^>]*>([\s\S]*?)<\/template>/i)
  const templateContent = templateMatch ? templateMatch[1] : ''

  const structure = parseJavaScriptContent(scriptContent, filePath)

  // Vue 特有的解析
  structure.template = {
    hasTemplate: !!templateContent,
    componentUsage: parseVueTemplateComponents(templateContent)
  }

  return structure
}

/**
 * 解析 JSON 文件内容
 * @param {string} content - 文件内容
 * @param {string} filePath - 文件路径
 * @returns {Object} 解析结果
 */
function parseJsonContent (content, filePath) {
  const structure = {
    imports: [],
    exports: [],
    functions: [],
    classes: [],
    hooks: [],
    components: []
  }

  try {
    const jsonData = JSON.parse(content)

    // 如果是 package.json，提取依赖信息
    if (path.basename(filePath) === 'package.json') {
      structure.dependencies = {
        dependencies: Object.keys(jsonData.dependencies || {}),
        devDependencies: Object.keys(jsonData.devDependencies || {}),
        scripts: Object.keys(jsonData.scripts || {})
      }
    }
  } catch (error) {
    // JSON 解析失败，忽略
  }

  return structure
}

/**
 * 解析通用文件内容
 * @param {string} content - 文件内容
 * @param {string} filePath - 文件路径
 * @returns {Object} 解析结果
 */
function parseGenericContent (content, _filePath) {
  return {
    imports: [],
    exports: [],
    functions: [],
    classes: [],
    hooks: [],
    components: [],
    lineCount: content.split('\n').length,
    hasContent: content.trim().length > 0
  }
}

/**
 * 解析导入语句
 * @param {string} content - 文件内容
 * @returns {Array} 导入信息
 */
function parseImports (content) {
  const imports = []

  // ES6 import 语句
  const importRegex = /import\s+(?:(?:\{([^}]+)\})|(?:(\w+))|(?:\*\s+as\s+(\w+)))\s+from\s+['"`]([^'"`]+)['"`]/g
  let match

  while ((match = importRegex.exec(content)) !== null) {
    const [, namedImports, defaultImport, namespaceImport, module] = match

    let items = []
    if (namedImports) {
      items = namedImports.split(',').map(item => item.trim())
    } else if (defaultImport) {
      items = [defaultImport]
    } else if (namespaceImport) {
      items = [namespaceImport]
    }

    imports.push({
      module,
      items,
      type: namedImports ? 'named' : defaultImport ? 'default' : 'namespace'
    })
  }

  // CommonJS require 语句
  const requireRegex = /(?:const|let|var)\s+(?:\{([^}]+)\}|(\w+))\s*=\s*require\(['"`]([^'"`]+)['"`]\)/g

  while ((match = requireRegex.exec(content)) !== null) {
    const [, destructured, variable, module] = match

    let items = []
    if (destructured) {
      items = destructured.split(',').map(item => item.trim())
    } else if (variable) {
      items = [variable]
    }

    imports.push({
      module,
      items,
      type: destructured ? 'destructured' : 'require'
    })
  }

  return imports
}

/**
 * 解析导出语句
 * @param {string} content - 文件内容
 * @returns {Array} 导出信息
 */
function parseExports (content) {
  const exports = []

  // export default
  const defaultExportRegex = /export\s+default\s+(?:(?:function\s+(\w+))|(?:class\s+(\w+))|(?:(\w+)))/g
  let match

  while ((match = defaultExportRegex.exec(content)) !== null) {
    const [, funcName, className, varName] = match
    const name = funcName || className || varName || 'default'

    exports.push({
      name,
      type: 'default',
      kind: funcName ? 'function' : className ? 'class' : 'variable'
    })
  }

  // named exports
  const namedExportRegex = /export\s+(?:(?:function\s+(\w+))|(?:class\s+(\w+))|(?:const\s+(\w+))|(?:let\s+(\w+))|(?:var\s+(\w+))|(?:\{([^}]+)\}))/g

  while ((match = namedExportRegex.exec(content)) !== null) {
    const [, funcName, className, constName, letName, varName, destructured] = match

    if (destructured) {
      const names = destructured.split(',').map(item => item.trim())
      names.forEach(name => {
        exports.push({
          name,
          type: 'named',
          kind: 'variable'
        })
      })
    } else {
      const name = funcName || className || constName || letName || varName
      exports.push({
        name,
        type: 'named',
        kind: funcName ? 'function' : className ? 'class' : 'variable'
      })
    }
  }

  // module.exports (CommonJS)
  const moduleExportsRegex = /module\.exports\s*=\s*(\w+)/g
  while ((match = moduleExportsRegex.exec(content)) !== null) {
    exports.push({
      name: match[1],
      type: 'module.exports',
      kind: 'variable'
    })
  }

  return exports
}

/**
 * 解析函数定义
 * @param {string} content - 文件内容
 * @returns {Array} 函数信息
 */
function parseFunctions (content) {
  const functions = []

  // 1. 函数声明
  const functionRegex = /(?:async\s+)?function\s+(\w+)\s*\([^)]*\)/g
  let match

  while ((match = functionRegex.exec(content)) !== null) {
    functions.push({
      name: match[1],
      type: 'function',
      async: match[0].includes('async')
    })
  }

  // 2. 箭头函数 (const/let/var)
  const arrowFunctionRegex = /(?:const|let|var)\s+(\w+)\s*=\s*(?:async\s+)?\([^)]*\)\s*=>/g

  while ((match = arrowFunctionRegex.exec(content)) !== null) {
    functions.push({
      name: match[1],
      type: 'arrow',
      async: match[0].includes('async')
    })
  }

  // 3. Vue 选项式 API 中的方法 (methods: { methodName() {} })
  const vueMethodsRegex = /methods\s*:\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}/s
  const methodsMatch = content.match(vueMethodsRegex)
  if (methodsMatch) {
    const methodsContent = methodsMatch[1]
    // 匹配方法定义：methodName() {} 或 async methodName() {}
    const methodRegex = /(?:async\s+)?(\w+)\s*\([^)]*\)\s*\{/g
    while ((match = methodRegex.exec(methodsContent)) !== null) {
      functions.push({
        name: match[1],
        type: 'vue-method',
        async: match[0].includes('async')
      })
    }
  }

  // 4. 对象方法简写 (在 setup() 或其他对象中)
  const objectMethodRegex = /(\w+)\s*\([^)]*\)\s*\{/g
  while ((match = objectMethodRegex.exec(content)) !== null) {
    const methodName = match[1]
    // 避免重复添加已经识别的函数
    if (!functions.some(f => f.name === methodName) &&
      !['if', 'for', 'while', 'switch', 'catch', 'function'].includes(methodName)) {
      functions.push({
        name: methodName,
        type: 'method',
        async: false
      })
    }
  }

  return functions
}

/**
 * 解析类定义
 * @param {string} content - 文件内容
 * @returns {Array} 类信息
 */
function parseClasses (content) {
  const classes = []
  const classRegex = /class\s+(\w+)(?:\s+extends\s+(\w+))?\s*\{/g
  let match

  while ((match = classRegex.exec(content)) !== null) {
    classes.push({
      name: match[1],
      extends: match[2] || null
    })
  }

  return classes
}

/**
 * 解析 React Hooks
 * @param {string} content - 文件内容
 * @returns {Array} Hook 信息
 */
function parseHooks (content) {
  const hooks = []
  const hookRegex = /use[A-Z]\w*/g
  let match

  while ((match = hookRegex.exec(content)) !== null) {
    if (!hooks.includes(match[0])) {
      hooks.push(match[0])
    }
  }

  return hooks
}

/**
 * 解析 React 组件
 * @param {string} content - 文件内容
 * @returns {Array} 组件信息
 */
function parseComponents (content) {
  const components = []

  // React 函数组件
  const componentRegex = /(?:function\s+([A-Z]\w*)|const\s+([A-Z]\w*)\s*=.*?(?:React\.FC|FunctionComponent))/g
  let match

  while ((match = componentRegex.exec(content)) !== null) {
    const name = match[1] || match[2]
    if (!components.includes(name)) {
      components.push(name)
    }
  }

  // JSX 元素（可能是组件使用）
  const jsxRegex = /<([A-Z]\w*)/g
  while ((match = jsxRegex.exec(content)) !== null) {
    if (!components.includes(match[1])) {
      components.push(match[1])
    }
  }

  return components
}

/**
 * 解析 Vue 模板中的组件使用
 * @param {string} templateContent - 模板内容
 * @returns {Array} 组件使用信息
 */
function parseVueTemplateComponents (templateContent) {
  const components = []
  const componentRegex = /<([A-Z][a-zA-Z0-9-]*)/g
  let match

  while ((match = componentRegex.exec(templateContent)) !== null) {
    if (!components.includes(match[1])) {
      components.push(match[1])
    }
  }

  return components
}

/**
 * 检查是否为支持的文件类型
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否支持
 */
function isSupportedFileType (filePath) {
  const supportedExtensions = [
    '.js', '.jsx', '.ts', '.tsx',
    '.vue', '.svelte',
    '.json', '.yaml', '.yml',
    '.md', '.txt', '.css', '.scss', '.less'
  ]

  const ext = path.extname(filePath).toLowerCase()
  return supportedExtensions.includes(ext)
}

/**
 * 检测前端框架
 * @param {Object} structure - 文件结构信息
 * @param {string} filePath - 文件路径
 * @returns {string} 前端框架
 */
function detectFramework (structure, filePath) {
  // Vue 文件
  if (path.extname(filePath) === '.vue') {
    return 'vue'
  }

  // Svelte 文件
  if (path.extname(filePath) === '.svelte') {
    return 'svelte'
  }

  // 基于导入判断框架
  const imports = structure.imports || []

  for (const importInfo of imports) {
    const module = importInfo.module.toLowerCase()

    // React
    if (module === 'react' || module.startsWith('react/')) {
      return 'react'
    }

    // Vue
    if (module === 'vue' || module.startsWith('@vue/')) {
      return 'vue'
    }

    // Angular
    if (module.startsWith('@angular/')) {
      return 'angular'
    }

    // Svelte
    if (module === 'svelte' || module.startsWith('svelte/')) {
      return 'svelte'
    }

    // Next.js
    if (module === 'next' || module.startsWith('next/')) {
      return 'nextjs'
    }

    // Nuxt
    if (module === 'nuxt' || module.startsWith('@nuxt/')) {
      return 'nuxt'
    }
  }

  // 基于 hooks 判断 React
  if (structure.hooks && structure.hooks.length > 0) {
    return 'react'
  }

  // 基于组件判断 React
  if (structure.components && structure.components.length > 0) {
    return 'react'
  }

  return 'unknown'
}

export {
  parseFilesStructure,
  analyzeFileRole,
  detectLanguage,
  detectFramework
}
