## Next.js 项目特定分析指导

在分析 Next.js 项目时，请特别关注：

### 渲染模式
- **SSR**: 服务端渲染的实现和优化
- **SSG**: 静态站点生成的使用场景
- **ISR**: 增量静态再生的配置
- **CSR**: 客户端渲染的选择时机

### 路由系统
- **文件路由**: pages 目录结构和动态路由
- **API Routes**: API 接口的实现和组织
- **中间件**: middleware.js 的使用
- **国际化**: i18n 路由配置

### 性能优化
- **图片优化**: next/image 的使用
- **字体优化**: next/font 的配置
- **代码分割**: 动态导入和懒加载
- **缓存策略**: 页面和 API 缓存配置

### 部署和配置
- **构建优化**: next.config.js 配置
- **环境变量**: 环境配置管理
- **部署平台**: Vercel、Netlify 等平台特性
- **CDN 配置**: 静态资源优化