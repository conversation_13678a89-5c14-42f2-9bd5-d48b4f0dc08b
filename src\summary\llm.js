/**
 * LLM 调用封装
 * 支持类 OpenAI 接口的 LLM 调用
 */
import https from 'https'
import axios from 'axios'
import { logger } from './logger.js'

/**
 * 调用 LLM 进行分析
 * @param {string} prompt - 提示词
 * @param {Object} config - 配置对象，包含 llm 字段
 * @param {Function} onChunk - 流式响应回调函数（可选）
 * @returns {string} LLM 响应结果
 */
async function callLLM (prompt, config, onChunk = null) {
  const { apiUrl, model, apiKey, maxTokens, temperature, rejectUnauthorized, stream } = config.llm || {}

  if (!apiKey) {
    throw new Error('LLM API Key 未配置，请在 .env 文件中设置 OPENAI_API_KEY')
  }

  try {
    logger.info('🤖 调用 LLM 进行分析...')
    logger.log(`📡 API URL: ${apiUrl}`)
    logger.log(`🤖 模型: ${model}`)
    logger.log(`📊 最大 Token: ${maxTokens}`)
    logger.log(`🌡️ 温度: ${temperature}`)
    logger.log(`🌊 流式响应: ${stream ? '启用' : '禁用'}`)

    const result = await callOpenAICompatible(prompt, {
      apiUrl,
      model,
      apiKey,
      maxTokens,
      temperature,
      rejectUnauthorized,
      stream
    }, onChunk)

    logger.success('LLM 调用成功')
    return result
  } catch (error) {
    logger.error('LLM 调用失败', error)
    throw error
  }
}

/**
 * 调用类 OpenAI 兼容的 API
 * @param {string} prompt - 提示词
 * @param {Object} options - 选项
 * @param {Function} onChunk - 流式响应回调函数（可选）
 * @returns {string} 响应结果
 */
async function callOpenAICompatible (prompt, options, onChunk = null) {
  const { model, apiKey, apiUrl, maxTokens, temperature, rejectUnauthorized = true, stream = false } = options

  if (!apiKey) {
    throw new Error('API Key 未配置')
  }

  const requestData = {
    model,
    messages: [
      {
        role: 'user',
        content: prompt
      }
    ],
    max_tokens: maxTokens,
    temperature,
    stream
  }

  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${apiKey}`
  }

  // 创建 HTTPS Agent 配置 SSL 验证
  const httpsAgent = new https.Agent({
    rejectUnauthorized
  })

  try {
    logger.log('📤 发送请求到 LLM API...')
    logger.log(`🔒 SSL证书验证: ${rejectUnauthorized ? '启用' : '禁用'}`)

    if (stream && onChunk) {
      // 流式响应处理
      return await handleStreamResponse(apiUrl, requestData, headers, httpsAgent, onChunk)
    } else {
      // 非流式响应处理
      return await handleNonStreamResponse(apiUrl, requestData, headers, httpsAgent)
    }

  } catch (error) {
    if (error.response) {
      // API 返回了错误响应
      const status = error.response.status
      const message = error.response.data?.error?.message || error.response.statusText
      throw new Error(`LLM API 错误 (${status}): ${message}`)
    } else if (error.request) {
      // 请求发送失败
      throw new Error(`网络请求失败: ${error.message}`)
    } else {
      // 其他错误
      throw new Error(`LLM 调用失败: ${error.message}`)
    }
  }
}

/**
 * 处理非流式响应
 * @param {string} apiUrl - API URL
 * @param {Object} requestData - 请求数据
 * @param {Object} headers - 请求头
 * @param {Object} httpsAgent - HTTPS Agent
 * @returns {string} 响应结果
 */
async function handleNonStreamResponse (apiUrl, requestData, headers, httpsAgent) {
  const response = await axios.post(`${apiUrl}/chat/completions`, requestData, {
    headers,
    timeout: 120000, // 2分钟超时
    httpsAgent
  })

  if (!response.data || !response.data.choices || response.data.choices.length === 0) {
    throw new Error('LLM API 返回数据格式错误')
  }

  const result = response.data.choices[0].message.content

  if (!result) {
    throw new Error('LLM API 返回内容为空')
  }

  logger.log(`📥 收到响应，长度: ${result.length} 字符`)
  return result.trim()
}

/**
 * 处理流式响应
 * @param {string} apiUrl - API URL
 * @param {Object} requestData - 请求数据
 * @param {Object} headers - 请求头
 * @param {Object} httpsAgent - HTTPS Agent
 * @param {Function} onChunk - 流式响应回调函数
 * @returns {string} 完整响应结果
 */
async function handleStreamResponse (apiUrl, requestData, headers, httpsAgent, onChunk) {
  return new Promise((resolve, reject) => {
    let fullResponse = ''
    let chunkCount = 0

    logger.log('🌊 开始流式响应...')

    const response = axios.post(`${apiUrl}/chat/completions`, requestData, {
      headers,
      timeout: 120000, // 2分钟超时
      httpsAgent,
      responseType: 'stream'
    })

    response.then(res => {
      res.data.on('data', chunk => {
        const chunkStr = chunk.toString()
        const lines = chunkStr.split('\n').filter(line => line.trim() !== '')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim()

            if (data === '[DONE]') {
              logger.log(`📥 流式响应完成，总长度: ${fullResponse.length} 字符，共 ${chunkCount} 个块`)
              resolve(fullResponse.trim())
              return
            }

            try {
              const parsed = JSON.parse(data)
              if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                const content = parsed.choices[0].delta.content
                fullResponse += content
                chunkCount++

                // 调用回调函数处理流式数据
                if (onChunk) {
                  onChunk(content, fullResponse)
                }
              }
            } catch (parseError) {
              // 忽略解析错误，继续处理下一个块
            }
          }
        }
      })

      res.data.on('end', () => {
        if (fullResponse.trim() === '') {
          reject(new Error('LLM API 返回内容为空'))
        } else {
          logger.log(`📥 流式响应结束，总长度: ${fullResponse.length} 字符`)
          resolve(fullResponse.trim())
        }
      })

      res.data.on('error', error => {
        reject(new Error(`流式响应错误: ${error.message}`))
      })

    }).catch(error => {
      reject(error)
    })
  })
}

/**
 * 验证 LLM 配置
 * @param {Object} config - 配置对象
 * @returns {boolean} 配置是否有效
 */
function validateLLMConfig (config) {
  const llmConfig = config.llm || {}
  const { apiKey, apiUrl, model, rejectUnauthorized } = llmConfig

  if (!apiKey) {
    logger.error('LLM API Key 未配置')
    return false
  }

  if (!apiUrl) {
    logger.error('LLM API URL 未配置')
    return false
  }

  if (!model) {
    logger.error('LLM 模型未配置')
    return false
  }

  // 验证SSL配置，如果未设置则使用安全默认值
  if (rejectUnauthorized === undefined) {
    logger.log('SSL验证配置未设置，使用安全默认值（启用SSL验证）')
  } else if (!rejectUnauthorized) {
    logger.warn('⚠️  SSL证书验证已禁用，请仅在开发环境中使用')
  }

  return true
}

export {
  callLLM,
  callOpenAICompatible,
  validateLLMConfig
}
