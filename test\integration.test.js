/**
 * 集成测试 - 验证新文件名格式
 */

import fs from 'fs'
import path from 'path'
const logger = require('../src/summary/logger')
const { processSummary } = require('../src/cli')

// 清理测试文件
function cleanupTestFiles() {
  const logsDir = path.join(process.cwd(), 'logs')
  const summaryDir = path.join(process.cwd(), 'summary')
  
  // 清理logs目录中的测试文件
  if (fs.existsSync(logsDir)) {
    const files = fs.readdirSync(logsDir)
    files.forEach(file => {
      if (file.includes('test-project')) {
        fs.unlinkSync(path.join(logsDir, file))
      }
    })
  }
  
  // 清理summary目录中的测试文件
  if (fs.existsSync(summaryDir)) {
    const files = fs.readdirSync(summaryDir)
    files.forEach(file => {
      if (file.includes('test-project')) {
        fs.unlinkSync(path.join(summaryDir, file))
      }
    })
  }
}

// 简单的测试框架
function test(description, testFn) {
  try {
    testFn()
    console.log(`✅ ${description}`)
  } catch (error) {
    console.error(`❌ ${description}: ${error.message}`)
  }
}

function assertTrue(condition, message = '') {
  if (!condition) {
    throw new Error(`Assertion failed: ${message}`)
  }
}

// 测试日志文件名格式
console.log('测试日志文件名格式:')

test('logger.js生成新格式的日志文件名', () => {
  // 清理之前的测试文件
  cleanupTestFiles()
  
  // 初始化日志文件
  const fileName = logger.initLogFile('/path/to/test-project', 'abcdef1234567', '1234567abcdef')
  
  // 验证文件名格式
  assertTrue(fileName.startsWith('test-project-'), '文件名应以项目名开头')
  assertTrue(fileName.includes('abcdef1'), '文件名应包含起始commit短ID')
  assertTrue(fileName.includes('1234567'), '文件名应包含结束commit短ID')
  assertTrue(fileName.endsWith('.log'), '文件名应以.log结尾')
  
  // 验证文件确实被创建
  const logsDir = path.join(process.cwd(), 'logs')
  const filePath = path.join(logsDir, fileName)
  assertTrue(fs.existsSync(filePath), '日志文件应该被创建')
  
  // 验证文件内容包含正确信息
  const content = fs.readFileSync(filePath, 'utf8')
  assertTrue(content.includes('AI Analyzer - 代码变更分析报告'), '文件应包含标题')
  assertTrue(content.includes('/path/to/test-project'), '文件应包含仓库路径')
})

test('处理包含特殊字符的项目路径', () => {
  // 清理之前的测试文件
  cleanupTestFiles()
  
  // 使用包含特殊字符的项目路径
  const fileName = logger.initLogFile('/path/to/my<>project test', 'abc1234', 'def5678')
  
  // 验证特殊字符被正确处理
  assertTrue(fileName.includes('my-project-test'), '特殊字符应被替换为连字符')
  assertTrue(!fileName.includes('<'), '不应包含<字符')
  assertTrue(!fileName.includes('>'), '不应包含>字符')
  assertTrue(!fileName.includes(' '), '不应包含空格')
})

// 模拟测试summary文件生成（由于需要完整的依赖，这里只测试文件名生成逻辑）
console.log('\n测试summary文件名生成逻辑:')

test('generateFileName函数生成正确的summary文件名', () => {
  const { generateFileName } = require('../src/summary/fileNameUtils')
  
  const fileName = generateFileName('/path/to/test-project', 'abcdef1234567', '1234567abcdef', 'md')
  
  // 验证文件名格式
  assertTrue(fileName.startsWith('test-project-'), '文件名应以项目名开头')
  assertTrue(fileName.includes('abcdef1'), '文件名应包含起始commit短ID')
  assertTrue(fileName.includes('1234567'), '文件名应包含结束commit短ID')
  assertTrue(fileName.endsWith('.md'), '文件名应以.md结尾')
  
  // 验证时间戳格式
  const parts = fileName.split('-')
  assertTrue(parts.length >= 4, '文件名应包含足够的部分')
})

test('处理边界情况', () => {
  const { generateFileName } = require('../src/summary/fileNameUtils')
  
  // 测试空路径
  const fileName1 = generateFileName('', 'abc1234', 'def5678', 'log')
  assertTrue(fileName1.startsWith('unknown-project-'), '空路径应使用默认项目名')
  
  // 测试空commit
  const fileName2 = generateFileName('/path/to/test', '', '', 'md')
  assertTrue(fileName2.includes('unknown'), '空commit应使用unknown')
})

console.log('\n集成测试完成!')

// 清理测试文件
cleanupTestFiles()