/**
 * Summary Logger 工厂函数
 * 为summary功能提供专用的日志接口，基于通用Logger创建配置好的实例
 */

import path from 'path'
import { generateFileName } from './fileNameUtils.js'
import { Logger } from '../utils/logger.js'

// 全局logger实例，供其他模块共享使用
let _logger = null

/**
 * 创建并初始化Summary专用的Logger实例
 * @param {string} repoPath - 仓库路径
 * @param {string} startCommit - 起始 commit
 * @param {string} endCommit - 结束 commit
 * @returns {Logger} 配置好的Logger实例
 */
function createSummaryLogger (repoPath, startCommit, endCommit) {
  const fileName = generateFileName(repoPath, startCommit, endCommit, 'log')
  const filePath = path.join(process.cwd(), 'logs', fileName)

  const header = {
    title: 'AI Analyzer - 代码变更分析报告',
    metadata: {
      '仓库路径': repoPath,
      '对比范围': `${startCommit} -> ${endCommit}`,
      '日志文件': fileName
    }
  }

  _logger = new Logger({
    filePath,
    header
  })
  console.log(`📝 日志将保存到: logs/${fileName}`)
  return _logger
}

export { createSummaryLogger, _logger as logger }
