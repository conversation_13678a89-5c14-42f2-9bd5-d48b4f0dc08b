# CLI 模块 (cli.js)

## 模块用途

CLI 模块是整个应用的入口点，负责：
- 解析命令行参数
- 协调各个模块的执行流程
- 处理错误和用户交互
- 保存分析结果到文件

## 主要函数

### main(repoPath, startCommit, endCommit)

执行完整的代码变更分析流程。

**参数：**
- `repoPath` (string): Git 仓库路径
- `startCommit` (string): 起始 commit ID
- `endCommit` (string): 结束 commit ID

**返回值：**
```javascript
{
  success: boolean,
  message: string,
  data: {
    commitInfo: Object,
    analysisData: Object,
    analysisSummary: Object,
    llmAnalysis: string,
    enhancedDiffs: Array,
    summary: Object
  }
}
```

**执行流程：**
1. 初始化日志文件
2. 加载配置
3. 获取 commit 信息
4. 获取增强版 diff 数据
5. 解析文件结构
6. 构建分析数据
7. 构建 AI 提示词
8. 调用 LLM 获取分析结果
9. 保存分析总结到文件
10. 输出结果到终端

### saveSummary(llmResult, startCommit, endCommit)

保存 LLM 分析总结到文件。

**参数：**
- `llmResult` (string): LLM 分析结果
- `startCommit` (string): 起始 commit
- `endCommit` (string): 结束 commit

**返回值：**
- `string`: 保存的文件名，失败时返回 null

**功能：**
- 自动创建 `summary` 目录
- 生成包含时间戳的唯一文件名
- 添加文件头信息（生成时间、commit 范围等）
- 保存 markdown 格式的分析结果

## 命令行接口

### summary 命令

```bash
node src/cli.js summary <repo-path> <start-commit> <end-commit>
```

**示例：**
```bash
# 基本用法
node src/cli.js summary /path/to/repo abc123 def456

# 使用 npm 脚本
npm run summary /path/to/repo abc123 def456
```

**配置说明：**
- 配置通过项目根目录的 `.env` 文件进行设置
- 缓存功能通过配置文件中的 `tokenCounter.enableCache` 控制
- 提示词压缩功能通过配置文件中的 `prompt.enableCompression` 控制

## 错误处理

CLI 模块实现了完整的错误处理机制：

- **参数验证错误**: 检查必需参数是否提供
- **配置加载错误**: 验证配置文件格式和必需字段
- **Git 操作错误**: 处理仓库不存在、commit 无效等情况
- **LLM 调用错误**: 处理 API 调用失败、网络超时等问题
- **文件操作错误**: 处理文件读写权限、磁盘空间等问题

所有错误都会记录到日志文件中，并向用户显示友好的错误信息。

## 输出文件

CLI 模块会生成以下输出文件：

### 日志文件
- **路径**: `logs/{projectName}-{timestamp}-{shortStart}-{shortEnd}.log`
- **格式**: 文本格式，包含详细的执行过程
- **内容**: 包括文件头信息、执行步骤、错误信息等

### 总结文件  
- **路径**: `summary/{projectName}-{timestamp}-{shortStart}-{shortEnd}.md`
- **格式**: Markdown 格式
- **内容**: LLM 生成的专业变更总结报告

文件名中的时间戳格式为 ISO 8601，确保每次运行都生成唯一的文件名。
