/**
 * 文件名工具函数单元测试
 */

const { extractProjectName, sanitizeFileName, generateFileName } = require('../src/summary/fileNameUtils')

// 简单的测试框架
function test (description, testFn) {
  try {
    testFn()
    console.log(`✅ ${description}`)
  } catch (error) {
    console.error(`❌ ${description}: ${error.message}`)
  }
}

function assertEqual (actual, expected, message = '') {
  if (actual !== expected) {
    throw new Error(`Expected "${expected}", but got "${actual}". ${message}`)
  }
}

// 测试 extractProjectName 函数
console.log('测试 extractProjectName 函数:')

test('正常路径提取项目名称', () => {
  assertEqual(extractProjectName('/path/to/my-project'), 'my-project')
  assertEqual(extractProjectName('C:\\Users\\<USER>\\my-project'), 'my-project')
  assertEqual(extractProjectName('./my-project'), 'my-project')
})

test('处理特殊字符路径', () => {
  assertEqual(extractProjectName('/path/to/my project'), 'my-project')
  assertEqual(extractProjectName('/path/to/my<>project'), 'my-project')
  assertEqual(extractProjectName('/path/to/项目名称'), '项目名称')
})

test('处理边界情况', () => {
  assertEqual(extractProjectName(''), 'unknown-project')
  assertEqual(extractProjectName(null), 'unknown-project')
  assertEqual(extractProjectName('.'), 'unnamed-project')
  assertEqual(extractProjectName('..'), 'unnamed-project')
})

// 测试 sanitizeFileName 函数
console.log('\n测试 sanitizeFileName 函数:')

test('清理特殊字符', () => {
  assertEqual(sanitizeFileName('my<>project'), 'my-project')
  assertEqual(sanitizeFileName('my:project'), 'my-project')
  assertEqual(sanitizeFileName('my/project'), 'my-project')
  assertEqual(sanitizeFileName('my\\project'), 'my-project')
})

test('处理空格和连字符', () => {
  assertEqual(sanitizeFileName('my project'), 'my-project')
  assertEqual(sanitizeFileName('my  project'), 'my-project')
  assertEqual(sanitizeFileName('my---project'), 'my-project')
  assertEqual(sanitizeFileName('-my-project-'), 'my-project')
})

test('处理边界情况', () => {
  assertEqual(sanitizeFileName(''), 'unnamed')
  assertEqual(sanitizeFileName(null), 'unnamed')
  assertEqual(sanitizeFileName('---'), 'unnamed')
})

// 测试 generateFileName 函数
console.log('\n测试 generateFileName 函数:')

test('生成正确格式的文件名', () => {
  const fileName = generateFileName('/path/to/my-project', 'abcdef1234567', '1234567abcdef', 'log')
  const parts = fileName.split('-')
  
  assertEqual(parts[0], 'my')
  assertEqual(parts[1], 'project')
  assertEqual(parts[parts.length - 2], 'abcdef1')
  assertEqual(parts[parts.length - 1], '1234567.log')
})

test('处理空提交ID', () => {
  const fileName = generateFileName('/path/to/my-project', '', '', 'md')
  const parts = fileName.split('-')
  
  assertEqual(parts[parts.length - 2], 'unknown')
  assertEqual(parts[parts.length - 1], 'unknown.md')
})

console.log('\n所有测试完成!')