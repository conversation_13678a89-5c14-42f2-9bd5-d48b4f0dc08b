/**
 * 配置加载器
 * 简化版本：只读取项目根目录 .env 文件
 */

import fs from 'fs'
import path from 'path'
import { logger } from './logger.js'

/**
 * 加载 .env 文件
 * @param {string} envPath - .env 文件路径
 * @returns {Object} 环境变量对象
 */
function loadEnvFile (envPath) {
  const envVars = {}

  if (!fs.existsSync(envPath)) {
    throw new Error(`未找到 .env 文件: ${envPath}\n请创建 .env 文件并配置必要的环境变量`)
  }

  try {
    const envContent = fs.readFileSync(envPath, 'utf8')
    const lines = envContent.split('\n')

    for (const line of lines) {
      const trimmedLine = line.trim()

      // 跳过空行和注释
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        continue
      }

      // 解析 KEY=VALUE 格式
      const equalIndex = trimmedLine.indexOf('=')
      if (equalIndex > 0) {
        const key = trimmedLine.slice(0, equalIndex).trim()
        let value = trimmedLine.slice(equalIndex + 1).trim()

        // 移除引号
        if ((value.startsWith('"') && value.endsWith('"')) ||
          (value.startsWith('\'') && value.endsWith('\''))) {
          value = value.slice(1, -1)
        }

        envVars[key] = value
      }
    }

    logger.log(`📄 加载 .env 文件: ${envPath}`)
    return envVars
  } catch (error) {
    throw new Error(`加载 .env 文件失败: ${error.message}`)
  }
}

/**
 * 加载默认配置
 * @returns {Promise<Object>} 默认配置对象
 */
async function loadDefaultConfig () {
  try {
    // ES6 模块中使用 import() 动态导入
    const defaultConfigPath = path.join(process.cwd(), 'src/default.config.js')
    const { default: config } = await import(`file://${defaultConfigPath}`)
    return config
  } catch (error) {
    throw new Error(`加载默认配置失败: ${error.message}`)
  }
}

/**
 * 应用环境变量到配置
 * @param {Object} config - 配置对象
 * @param {Object} envVars - 环境变量对象
 * @returns {Object} 应用环境变量后的配置
 */
function applyEnvVars (config, envVars) {
  const newConfig = JSON.parse(JSON.stringify(config)) // 深拷贝

  // 应用 LLM 相关环境变量
  if (envVars.OPENAI_API_URL) {
    newConfig.llm.apiUrl = envVars.OPENAI_API_URL
  }

  if (envVars.OPENAI_MODEL) {
    newConfig.llm.model = envVars.OPENAI_MODEL
  }

  if (envVars.OPENAI_API_KEY) {
    newConfig.llm.apiKey = envVars.OPENAI_API_KEY
  }

  if (envVars.OPENAI_MAX_TOKENS) {
    const maxTokens = parseInt(envVars.OPENAI_MAX_TOKENS)
    if (!isNaN(maxTokens)) {
      newConfig.llm.maxTokens = maxTokens
    }
  }

  if (envVars.OPENAI_TEMPERATURE) {
    const temperature = parseFloat(envVars.OPENAI_TEMPERATURE)
    if (!isNaN(temperature)) {
      newConfig.llm.temperature = temperature
    }
  }

  // 应用 SSL 证书验证配置
  if (envVars.OPENAI_REJECT_UNAUTHORIZED !== undefined) {
    const value = envVars.OPENAI_REJECT_UNAUTHORIZED.toLowerCase()
    if (value === 'false') {
      newConfig.llm.rejectUnauthorized = false
    } else if (value === 'true') {
      newConfig.llm.rejectUnauthorized = true
    }
  }

  // 应用提示词压缩配置
  if (envVars.PROMPT_ENABLE_COMPRESSION !== undefined) {
    const value = envVars.PROMPT_ENABLE_COMPRESSION.toLowerCase()
    if (value === 'true') {
      newConfig.prompt.enableCompression = true
    } else if (value === 'false') {
      newConfig.prompt.enableCompression = false
    }
  }

  // 应用流式响应配置
  if (envVars.OPENAI_STREAM !== undefined) {
    const value = envVars.OPENAI_STREAM.toLowerCase()
    if (value === 'true') {
      newConfig.llm.stream = true
    } else if (value === 'false') {
      newConfig.llm.stream = false
    }
  }

  return newConfig
}

/**
 * 加载配置文件
 * @returns {Object} 合并后的配置对象
 */
async function loadConfig () {
  logger.log('📋 正在加载配置文件...')

  // 1. 加载默认配置
  let config = await loadDefaultConfig()
  logger.log('✅ 默认配置加载完成')

  // 2. 加载项目根目录 .env 文件
  const envPath = path.join(process.cwd(), '.env')
  const envVars = loadEnvFile(envPath)

  // 3. 应用环境变量
  config = applyEnvVars(config, envVars)

  logger.log('✅ 配置加载完成')

  logger.log('📊 当前配置:')
  logger.log(`  🎯 项目类型: ${config.project.type}`)
  logger.log(`  🤖 LLM 模型: ${config.llm.model}`)
  logger.log(`  🔗 API URL: ${config.llm.apiUrl}`)
  logger.log(`  🔒 SSL 验证: ${config.llm.rejectUnauthorized ? '启用' : '禁用'}`)
  logger.log(`  🌊 流式响应: ${config.llm.stream ? '启用' : '禁用'}`)
  logger.log(`  💾 缓存: ${config.analysis.enableCache ? '启用' : '禁用'}`)
  logger.log(`  🗜️ 提示词压缩: ${config.prompt.enableCompression ? '启用' : '禁用'}`)

  return config
}

/**
 * 获取配置摘要信息
 * @param {Object} config - 配置对象
 * @returns {Object} 配置摘要
 */
function getConfigSummary (config) {
  return {
    projectType: config.project.type,
    llmModel: config.llm.model,
    apiUrl: config.llm.apiUrl,
    cacheEnabled: config.analysis.enableCache,
    debugEnabled: config.debug.enabled
  }
}

export {
  loadConfig,
  getConfigSummary
}
