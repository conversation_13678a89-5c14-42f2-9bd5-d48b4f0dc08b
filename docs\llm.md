# LLM 调用模块 (llm.js)

## 模块用途

LLM 调用模块负责与 AI 模型进行交互，提供：
- OpenAI API 调用封装
- 配置验证和错误处理
- 请求重试和超时控制
- 响应格式化和处理

## 主要函数

### callLLM(prompt, config, onChunk)

调用 LLM 获取分析结果，是模块的核心函数。

**参数：**
- `prompt` (string): 提示词文本
- `config` (Object): 配置信息
- `onChunk` (Function, 可选): 流式响应回调函数

**返回值：**
- `string`: LLM 生成的分析结果

**功能：**
- 发送 HTTP 请求到 OpenAI API
- 支持流式和非流式响应模式
- 处理 API 响应和错误
- 提取生成的文本内容
- 记录调用统计信息

**流式响应回调：**
```javascript
onChunk(chunk, fullContent) => {
  // chunk: 当前接收到的文本片段
  // fullContent: 到目前为止的完整内容
}
```

### validateLLMConfig(config)

验证 LLM 配置是否有效。

**参数：**
- `config` (Object): 配置对象

**返回值：**
- `boolean`: 配置是否有效

**验证项目：**
- API Key 是否存在
- API URL 格式是否正确
- 模型名称是否有效
- 参数范围是否合理

### buildRequestPayload(prompt, config)

构建 API 请求负载。

**参数：**
- `prompt` (string): 提示词
- `config` (Object): 配置信息

**返回值：**
```javascript
{
  model: string,
  messages: Array,
  max_tokens: number,
  temperature: number,
  stream: boolean
}
```

### handleAPIResponse(response)

处理 API 响应。

**参数：**
- `response` (Object): HTTP 响应对象

**返回值：**
- `string`: 提取的文本内容

**处理逻辑：**
- 检查响应状态码
- 解析 JSON 响应体
- 提取生成的文本
- 处理错误响应

### handleAPIError(error, config)

处理 API 调用错误。

**参数：**
- `error` (Error): 错误对象
- `config` (Object): 配置信息

**功能：**
- 分类错误类型
- 记录详细错误信息
- 提供用户友好的错误消息
- 决定是否重试

## 错误处理

### 错误类型

- **配置错误**: API Key 缺失、URL 无效
- **网络错误**: 连接超时、网络不可达
- **API 错误**: 认证失败、配额超限、模型不可用
- **响应错误**: 格式错误、内容截断

### 重试机制

模块实现了智能重试机制：
- 网络错误：最多重试 3 次
- 速率限制：指数退避重试
- 服务器错误：短暂延迟后重试
- 客户端错误：不重试

### 超时控制

- 连接超时：30 秒
- 读取超时：60 秒
- 总超时：配置中的 timeout 值

## 配置选项

### 必需配置

- `apiKey`: OpenAI API 密钥
- `apiUrl`: API 端点地址
- `model`: 使用的模型名称

### 可选配置

- `maxTokens`: 最大生成 token 数（默认 4000）
- `temperature`: 生成随机性（默认 0.1）
- `timeout`: 请求超时时间（默认 60000ms）
- `stream`: 是否启用流式响应（默认 false）
- `rejectUnauthorized`: SSL证书验证（默认 true）

### 支持的模型

- `gpt-4`: GPT-4 模型
- `gpt-4-turbo`: GPT-4 Turbo 模型
- `gpt-3.5-turbo`: GPT-3.5 Turbo 模型

## 请求格式

模块使用 OpenAI Chat Completions API 格式：

```javascript
{
  model: "gpt-4",
  messages: [
    {
      role: "user",
      content: "提示词内容"
    }
  ],
  max_tokens: 4000,
  temperature: 0.1,
  stream: false  // 或 true 启用流式响应
}
```

## 流式响应

### 启用流式响应

在配置中设置 `stream: true` 或通过环境变量 `OPENAI_STREAM=true`：

```javascript
const config = {
  llm: {
    stream: true,
    // 其他配置...
  }
}

// 使用流式响应
const result = await callLLM(prompt, config, (chunk, fullContent) => {
  // 实时处理每个文本片段
  console.log('接收到:', chunk)
  // 实时写入文件或更新UI
})
```

### 流式响应优势

- **实时反馈**: 立即看到生成过程
- **更好的用户体验**: 避免长时间等待
- **实时写入**: 边生成边保存到文件
- **错误恢复**: 部分内容可以保留

### 流式数据格式

流式响应使用 Server-Sent Events (SSE) 格式：

```
data: {"choices":[{"delta":{"content":"文本片段"}}]}

data: [DONE]
```

## 响应处理

### 成功响应

从 API 响应中提取生成的文本：

```javascript
response.data.choices[0].message.content
```

### 错误响应

处理各种错误情况：
- 401: API Key 无效
- 429: 请求频率超限
- 500: 服务器内部错误
- 503: 服务不可用

## 日志记录

模块记录详细的调用信息：
- 请求参数（不包含敏感信息）
- 响应状态和耗时
- 错误详情和重试次数
- Token 使用统计

## 安全考虑

- API Key 不会记录到日志中
- 使用 HTTPS 进行安全传输
- 验证响应内容格式
- 限制请求大小和频率
