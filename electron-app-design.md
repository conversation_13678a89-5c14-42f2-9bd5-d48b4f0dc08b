# AI Analyzer Electron 应用设计方案

## 项目概述

将现有的 AI 代码分析 CLI 工具封装为 Electron 桌面应用，提供图形化界面，让用户可以通过点击操作完成 Git 项目的代码变更分析。

## 1. 目录结构设计

```
ai-analyzer-desktop/
├── package.json                    # 项目配置
├── electron.js                     # Electron 主进程入口
├── preload.js                      # 预加载脚本
├── dist/                           # 打包输出目录
├── public/                         # 静态文件
│   ├── index.html                  # HTML 模板
│   └── favicon.ico                 # 应用图标
├── src/                            # 前端源码
│   ├── main.js                     # Vue 应用入口
│   ├── App.vue                     # 根组件
│   ├── router/                     # 路由配置
│   │   └── index.js                # 路由定义
│   ├── views/                      # 页面组件
│   │   ├── SummaryPage.vue         # 主页面（项目选择+提交选择+分析）
│   │   └── SettingsPage.vue        # 配置页面
│   ├── utils/                      # 工具函数
│   │   └── electron-api.js         # Electron API 封装
│   └── assets/                     # 静态资源
│       └── styles/
│           └── main.less           # 全局样式
├── core/                           # 核心分析引擎（现有代码）
│   ├── src/                        # 原有的分析代码
│   │   ├── cli.js
│   │   ├── summary/
│   │   ├── utils/
│   │   └── default.config.js
│   ├── logs/                       # 日志输出目录
│   └── summary/                    # 分析结果输出目录
└── build/                          # 构建配置
    ├── icon.png                    # 应用图标
    └── build-config.js             # 打包配置
```

## 2. 技术栈选择

### 前端框架
- **Vue 3** + Composition API
- **Vue Router 4** 路由管理
- **Element Plus** UI 组件库
- **Less** CSS 预处理器

### Electron 相关
- **Electron** 最新稳定版
- **electron-builder** 打包工具

### 开发工具
- **Vite** 构建工具
- **simple-git** Git 操作库

### 暂不引入的技术
- ~~Pinia/Vuex~~（使用 Vue 3 响应式数据）
- ~~TypeScript~~（保持 JavaScript 简单）
- ~~ESLint/Prettier~~（后期需要时再引入）

## 3. 页面功能设计

### 3.1 主页面（SummaryPage.vue）

```
┌─────────────────────────────────────────────────────────┐
│  AI 代码分析工具                              [设置] [×] │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 📁 项目选择                                              │
│   [选择 Git 项目文件夹] 📂                               │
│   📍 当前项目: /path/to/your/project                     │
│                                                         │
│ 📋 提交记录选择                                          │
│   ┌─────────────────────────────────────────────────┐   │
│   │ ☐ abc1234 - 2024-01-15 - 修复登录bug (张三)     │   │
│   │ ☑ def5678 - 2024-01-14 - 添加用户管理功能 (李四) │   │ ← 结束提交
│   │ ☑ ghi9012 - 2024-01-13 - 初始化项目 (王五)      │   │ ← 起始提交
│   │ ☐ hij3456 - 2024-01-12 - 项目初始化 (赵六)      │   │
│   └─────────────────────────────────────────────────┘   │
│   📌 对比范围: ghi9012 → def5678                         │
│                                                         │
│ 🚀 操作区域                                              │
│   [开始分析] [清空日志]                                   │
│                                                         │
│ � 实析时日志                                              │
│   ┌─────────────────────────────────────────────────┐   │
│   │ [10:30:15] 🔍 开始分析代码变更...                │   │
│   │ [10:30:16] 📂 获取 Git 差异信息...               │   │
│   │ [10:30:17] 🤖 调用 LLM API 分析...               │   │
│   │ [10:30:18] ✅ 分析完成                           │   │
│   └─────────────────────────────────────────────────┘   │
│                                                         │
│ 📄 分析结果                                              │
│   ┌─────────────────────────────────────────────────┐   │
│   │ # 代码变更分析报告                               │   │
│   │                                                 │   │
│   │ ## 总体概述                                     │   │
│   │ 本次变更为 Vue 项目，主要涉及用户管理模块...      │   │
│   │                                                 │   │
│   │ ## 功能变更                                     │   │
│   │ - [新增] 用户管理页面，支持用户增删改查...        │   │
│   │                                                 │   │
│   │ [📋 复制结果] [💾 保存为MD] [📄 保存为PDF]        │   │
│   └─────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

### 3.2 配置页面（SettingsPage.vue）

```
┌─────────────────────────────────────────────────────────┐
│  配置设置                                    [返回] [×] │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 🤖 LLM 配置                                              │
│   API URL:    [https://api.openai.com/v1     ] 🔗       │
│   API Key:    [sk-xxxxxxxxxxxxxxxx           ] 🔑       │
│   模型:       [gpt-4                         ] 📋       │
│   最大Token:  [16000                         ] 📊       │
│   温度:       [0.1                           ] 🌡️       │
│                                                         │
│ ⚙️ 分析配置                                               │
│   启用提示词压缩: ☐                                      │
│   SSL证书验证:   ☑                                      │
│   流式响应:      ☑                                      │
│                                                         │
│ � 配置操作                                               │
│   [保存配置]                                             │
│                                                         │
│ ✅ 配置状态: 已保存 (2024-01-15 10:30:00)                │
└─────────────────────────────────────────────────────────┘
```

### 3.3 页面交互逻辑

**主页面交互流程：**
1. 用户点击"选择项目文件夹" → 打开文件夹选择对话框
2. 选择项目后 → 自动加载Git提交记录
3. 用户选择两个提交（起始和结束）→ 显示对比范围
4. 点击"开始分析" → 实时更新日志
5. 分析完成 → 在结果区域显示Markdown格式的报告

**配置页面交互流程：**
1. 用户修改配置项 → 实时验证输入格式
2. 点击"保存配置" → 保存到本地配置文件
3. 点击"返回" → 回到主页面

**错误处理：**
- Git项目无效 → 显示错误提示
- 配置参数错误 → 高亮错误字段
- 分析过程出错 → 在日志区域显示错误信息

## 4. 数据流和通信设计

### 4.1 应用状态管理（使用 Vue 3 响应式）
```javascript
// 各页面组件内的状态管理
// SummaryPage.vue
const summaryState = reactive({
  // 项目相关
  selectedProject: null,
  projectInfo: {},
  
  // Git 相关
  commits: [],
  selectedCommits: {
    start: null,
    end: null
  },
  
  // 分析相关
  isAnalyzing: false,
  logs: [],
  currentResult: null,
  currentLogFile: null,
  currentResultFile: null
})

// SettingsPage.vue
const settingsState = reactive({
  config: {},
  isSaving: false,
  lastSaved: null
})
```

### 4.2 IPC 通信设计

**设计原则：**
- 渲染进程负责UI交互和简单逻辑
- 主进程负责文件系统操作、Git操作、CLI调用
- 保持安全性，避免在渲染进程直接调用Node.js API

```javascript
// 渲染进程 -> 主进程
'select-project-folder'     // 选择项目文件夹
'get-git-commits'          // 获取 Git 提交记录
'start-analysis'           // 开始分析
'save-config'              // 保存配置文件
'load-config'              // 加载配置文件

// 主进程 -> 渲染进程
'project-selected'         // 项目选择完成
'commits-loaded'           // 提交记录加载完成
'analysis-started'         // 分析开始
'analysis-log'             // 实时日志输出
'analysis-complete'        // 分析完成
'analysis-error'           // 分析错误
```

**为什么不在渲染进程直接操作：**
1. **安全性**：避免渲染进程直接访问文件系统
2. **稳定性**：Git操作和CLI调用可能阻塞，放在主进程避免UI卡顿
3. **权限控制**：主进程可以更好地控制文件访问权限

### 4.3 CLI集成改造需求

**现有问题：**
- CLI工具生成的日志文件名包含时间戳，UI无法预知
- 没有实时日志流，只能读取文件

**改造方案：**
修改CLI工具，添加回调函数支持，返回文件路径

```javascript
// 改造后的CLI调用
async function startAnalysis(projectPath, startCommit, endCommit) {
  const result = await processSummary(projectPath, startCommit, endCommit, {
    onLog: (log) => {
      mainWindow.webContents.send('analysis-log', log);
    }
  });
  
  // 返回文件路径
  mainWindow.webContents.send('analysis-complete', {
    result: result,
    logFile: result.logFile,
    summaryFile: result.summaryFile
  });
}
```

## 6. 核心功能实现方案

### 6.1 Git 操作集成
```javascript
// 在主进程中使用 simple-git
const simpleGit = require('simple-git');

// 获取提交记录
async function getCommits(projectPath) {
  const git = simpleGit(projectPath);
  const log = await git.log(['--oneline', '--graph', '--decorate', '--all']);
  return log.all.map(commit => ({
    hash: commit.hash,
    date: commit.date,
    message: commit.message,
    author: commit.author_name
  }));
}
```

### 6.2 分析引擎集成

**现有CLI问题：**
- 日志文件名动态生成（包含时间戳），UI无法预知文件路径
- 没有实时日志回调，只能事后读取文件

**改造方案：**

```javascript
// 修改CLI工具，添加回调支持
const { processSummary } = require('./core/src/cli.js');

async function startAnalysis(projectPath, startCommit, endCommit) {
  try {
    const result = await processSummary(projectPath, startCommit, endCommit, {
      // 添加回调函数
      onLog: (logMessage) => {
        mainWindow.webContents.send('analysis-log', logMessage);
      },
      onLogFileCreated: (logFilePath) => {
        // 通知UI日志文件路径
        mainWindow.webContents.send('log-file-created', logFilePath);
      }
    });
    
    // 返回完整结果信息
    mainWindow.webContents.send('analysis-complete', {
      result: result.data,
      logFile: result.logFile,
      summaryFile: result.summaryFile
    });
  } catch (error) {
    mainWindow.webContents.send('analysis-error', error.message);
  }
}
```

**CLI改造的具体位置：**
1. 修改 `src/summary/logger.js` 添加事件回调
2. 修改 `src/summary/summaryProcessor.js` 传递回调函数
3. 返回文件路径信息

### 6.3 配置管理
```javascript
// 配置文件读写
const fs = require('fs');
const path = require('path');

const configPath = path.join(__dirname, 'config.json');

function saveConfig(config) {
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
}

function loadConfig() {
  if (fs.existsSync(configPath)) {
    return JSON.parse(fs.readFileSync(configPath, 'utf8'));
  }
  return getDefaultConfig();
}
```



## 5. 技术实现思路

### 5.1 项目依赖
主要依赖包括：Vue 3、Vue Router、Element Plus、Less、Electron、electron-builder、Vite、simple-git 等。具体版本在实现时根据最新稳定版本确定。

### 5.2 Electron 主进程
- 创建主窗口，配置安全的 webPreferences
- 处理 IPC 通信，调用 Git 操作和 CLI 工具
- 管理文件系统操作和配置文件读写

### 5.3 预加载脚本
- 使用 contextBridge 安全地暴露 API 给渲染进程
- 封装 IPC 通信方法，提供统一的接口
- 处理事件监听和回调函数

## 7. CLI改造需求详细说明

### 7.1 现有CLI的问题

1. **日志文件路径不可预知**
   - 文件名包含时间戳：`project-2024-01-15T10-30-15-123Z-abc1234-def5678.log`
   - UI无法提前知道文件路径，无法读取日志

2. **缺少实时反馈机制**
   - 没有进度回调
   - 没有实时日志流
   - UI只能等待分析完成

3. **结果文件路径问题**
   - 同样包含时间戳，UI无法预知路径

### 7.2 改造方案

1. **修改 `processSummary` 函数签名**
```javascript
// 原来
async function processSummary(repoPath, startCommit, endCommit)

// 改造后
async function processSummary(repoPath, startCommit, endCommit, callbacks = {})
```

2. **添加回调函数支持**
```javascript
const callbacks = {
  onLog: (message) => {},       // 日志回调
  onLogFileCreated: (path) => {}, // 日志文件创建回调
  onResultFileCreated: (path) => {} // 结果文件创建回调
}
```

3. **修改返回值**
```javascript
// 原来返回分析数据
return result

// 改造后返回包含文件路径的对象
return {
  data: result,
  logFile: logFilePath,
  summaryFile: summaryFilePath
}
```

**具体修改位置：**
- `src/summary/summaryProcessor.js` - 主函数添加回调参数
- `src/summary/logger.js` - 添加实时日志回调

## 8. 开发阶段规划

### 阶段一：基础架构（1-2天）
1. 初始化 Electron + Vue 项目
2. 配置 Vite 构建和 Less 预处理
3. 实现基本的窗口和 Vue Router 路由
4. 集成 Element Plus UI 组件库

### 阶段二：核心功能（3-4天）
1. 实现项目文件夹选择功能
2. 集成 Git 操作，显示提交记录列表
3. 实现提交记录的双选功能
4. 集成现有的分析引擎，添加回调支持

### 阶段三：用户体验（2-3天）
1. 实现实时日志显示功能
2. 完善结果展示和导出功能
3. 添加错误处理和用户反馈
4. 优化界面交互和样式

### 阶段四：配置和优化（1-2天）
1. 实现配置页面和配置管理
2. 应用打包和测试
3. 性能优化和bug修复

## 9. 总结

这个设计方案具有以下特点：

### 优势
- **简单易懂**：只有两个主要页面，功能清晰
- **快速开发**：技术栈成熟，开发效率高
- **易于维护**：代码结构简单，便于后期扩展
- **用户友好**：图形化界面，操作直观

### 核心功能
- Git 项目选择和提交记录展示
- 双提交选择机制
- 实时日志显示
- 结果展示和导出
- 配置管理

### 扩展性
- 后期可以根据需要添加更多功能
- 可以引入状态管理库
- 可以添加更多的 UI 组件
- 可以增加更多的配置选项

这个方案为快速原型开发提供了良好的基础，同时保持了足够的灵活性以支持未来的功能扩展。