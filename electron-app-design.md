# AI Analyzer Electron 应用设计方案

## 项目概述

将现有的 AI 代码分析 CLI 工具封装为 Electron 桌面应用，提供图形化界面，让用户可以通过点击操作完成 Git 项目的代码变更分析。

## 1. 目录结构设计（修改版）

**建议采用最小侵入式的目录结构，避免重构现有代码：**

```
project-analytics/                  # 现有项目根目录（保持不变）
├── src/                            # 现有CLI代码（保持不变）
│   ├── cli.js
│   ├── summary/
│   ├── utils/
│   └── default.config.js
├── logs/                           # 现有日志目录（保持不变）
├── summary/                        # 现有总结目录（保持不变）
├── package.json                    # 现有CLI配置（保持不变）
├── electron-app/                   # 新增：Electron应用目录
│   ├── package.json                # Electron应用专用配置
│   ├── electron.js                 # Electron 主进程入口
│   ├── preload.js                  # 预加载脚本
│   ├── public/                     # 静态文件
│   │   ├── index.html              # HTML 模板
│   │   └── favicon.ico             # 应用图标
│   ├── src/                        # Vue前端源码
│   │   ├── main.js                 # Vue 应用入口
│   │   ├── App.vue                 # 根组件
│   │   ├── router/                 # 路由配置
│   │   │   └── index.js            # 路由定义
│   │   ├── views/                  # 页面组件
│   │   │   ├── SummaryPage.vue     # 主页面
│   │   │   └── SettingsPage.vue    # 配置页面
│   │   ├── utils/                  # 工具函数
│   │   │   └── electron-api.js     # Electron API 封装
│   │   └── assets/                 # 静态资源
│   │       └── styles/
│   │           └── main.less       # 全局样式
│   ├── dist/                       # 构建输出目录
│   └── build/                      # 构建配置
│       ├── icon.png                # 应用图标
│       └── build-config.js         # 打包配置
└── README.md                       # 项目说明
```

**优势：**
- 现有CLI代码完全不需要修改
- 可以独立开发和测试Electron应用
- 降低开发风险，便于回滚
- 保持现有项目结构的稳定性

## 2. 技术栈选择

### 前端框架
- **Vue 3** + Composition API
- **Vue Router 4** 路由管理
- **Element Plus** UI 组件库
- **Less** CSS 预处理器

### Electron 相关
- **Electron** 最新稳定版
- **electron-builder** 打包工具

### 开发工具
- **Vite** 构建工具
- **simple-git** Git 操作库

### 暂不引入的技术
- ~~Pinia/Vuex~~（使用 Vue 3 响应式数据）
- ~~TypeScript~~（保持 JavaScript 简单）
- ~~ESLint/Prettier~~（后期需要时再引入）

## 3. 页面功能设计

### 3.1 主页面（SummaryPage.vue）

```
┌─────────────────────────────────────────────────────────┐
│  AI 代码分析工具                              [设置] [×] │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 📁 项目选择                                              │
│   [选择 Git 项目文件夹] 📂                               │
│   📍 当前项目: /path/to/your/project                     │
│                                                         │
│ 📋 提交记录选择                                          │
│   ┌─────────────────────────────────────────────────┐   │
│   │ ☐ abc1234 - 2024-01-15 - 修复登录bug (张三)     │   │
│   │ ☑ def5678 - 2024-01-14 - 添加用户管理功能 (李四) │   │ ← 结束提交
│   │ ☑ ghi9012 - 2024-01-13 - 初始化项目 (王五)      │   │ ← 起始提交
│   │ ☐ hij3456 - 2024-01-12 - 项目初始化 (赵六)      │   │
│   └─────────────────────────────────────────────────┘   │
│   📌 对比范围: ghi9012 → def5678                         │
│                                                         │
│ 🚀 操作区域                                              │
│   [开始分析] [清空日志]                                   │
│                                                         │
│ � 实析时日志                                              │
│   ┌─────────────────────────────────────────────────┐   │
│   │ [10:30:15] 🔍 开始分析代码变更...                │   │
│   │ [10:30:16] 📂 获取 Git 差异信息...               │   │
│   │ [10:30:17] 🤖 调用 LLM API 分析...               │   │
│   │ [10:30:18] ✅ 分析完成                           │   │
│   └─────────────────────────────────────────────────┘   │
│                                                         │
│ 📄 分析结果                                              │
│   ┌─────────────────────────────────────────────────┐   │
│   │ # 代码变更分析报告                               │   │
│   │                                                 │   │
│   │ ## 总体概述                                     │   │
│   │ 本次变更为 Vue 项目，主要涉及用户管理模块...      │   │
│   │                                                 │   │
│   │ ## 功能变更                                     │   │
│   │ - [新增] 用户管理页面，支持用户增删改查...        │   │
│   │                                                 │   │
│   │ [📋 复制结果] [💾 保存为MD] [📄 保存为PDF]        │   │
│   └─────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

### 3.2 配置页面（SettingsPage.vue）

```
┌─────────────────────────────────────────────────────────┐
│  配置设置                                    [返回] [×] │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 🤖 LLM 配置                                              │
│   API URL:    [https://api.openai.com/v1     ] 🔗       │
│   API Key:    [sk-xxxxxxxxxxxxxxxx           ] 🔑       │
│   模型:       [gpt-4                         ] 📋       │
│   最大Token:  [16000                         ] 📊       │
│   温度:       [0.1                           ] 🌡️       │
│                                                         │
│ ⚙️ 分析配置                                               │
│   启用提示词压缩: ☐                                      │
│   SSL证书验证:   ☑                                      │
│   流式响应:      ☑                                      │
│                                                         │
│ � 配置操作                                               │
│   [保存配置]                                             │
│                                                         │
│ ✅ 配置状态: 已保存 (2024-01-15 10:30:00)                │
└─────────────────────────────────────────────────────────┘
```

### 3.3 页面交互逻辑

**主页面交互流程：**
1. 用户点击"选择项目文件夹" → 打开文件夹选择对话框
2. 选择项目后 → 自动加载Git提交记录
3. 用户选择两个提交（起始和结束）→ 显示对比范围
4. 点击"开始分析" → 实时更新日志
5. 分析完成 → 在结果区域显示Markdown格式的报告

**配置页面交互流程：**
1. 用户修改配置项 → 实时验证输入格式
2. 点击"保存配置" → 保存到本地配置文件
3. 点击"返回" → 回到主页面

**错误处理：**
- Git项目无效 → 显示错误提示
- 配置参数错误 → 高亮错误字段
- 分析过程出错 → 在日志区域显示错误信息

## 4. 数据流和通信设计

### 4.1 应用状态管理（使用 Vue 3 响应式）
```javascript
// 各页面组件内的状态管理
// SummaryPage.vue
const summaryState = reactive({
  // 项目相关
  selectedProject: null,
  projectInfo: {},
  
  // Git 相关
  commits: [],
  selectedCommits: {
    start: null,
    end: null
  },
  
  // 分析相关
  isAnalyzing: false,
  logs: [],
  currentResult: null,
  currentLogFile: null,
  currentResultFile: null
})

// SettingsPage.vue
const settingsState = reactive({
  config: {},
  isSaving: false,
  lastSaved: null
})
```

### 4.2 IPC 通信设计

**设计原则：**
- 渲染进程负责UI交互和简单逻辑
- 主进程负责文件系统操作、Git操作、CLI调用
- 保持安全性，避免在渲染进程直接调用Node.js API

```javascript
// 渲染进程 -> 主进程
'select-project-folder'     // 选择项目文件夹
'get-git-commits'          // 获取 Git 提交记录
'start-analysis'           // 开始分析
'save-config'              // 保存配置文件
'load-config'              // 加载配置文件

// 主进程 -> 渲染进程
'project-selected'         // 项目选择完成
'commits-loaded'           // 提交记录加载完成
'analysis-started'         // 分析开始
'analysis-log'             // 实时日志输出
'analysis-complete'        // 分析完成
'analysis-error'           // 分析错误
```

**为什么不在渲染进程直接操作：**
1. **安全性**：避免渲染进程直接访问文件系统
2. **稳定性**：Git操作和CLI调用可能阻塞，放在主进程避免UI卡顿
3. **权限控制**：主进程可以更好地控制文件访问权限

### 4.3 CLI集成改造需求

**现有问题：**
- CLI工具生成的日志文件名包含时间戳，UI无法预知
- 没有实时日志流，只能读取文件

**改造方案：**
修改CLI工具，添加回调函数支持，返回文件路径

```javascript
// 改造后的CLI调用
async function startAnalysis(projectPath, startCommit, endCommit) {
  const result = await processSummary(projectPath, startCommit, endCommit, {
    onLog: (log) => {
      mainWindow.webContents.send('analysis-log', log);
    }
  });
  
  // 返回文件路径
  mainWindow.webContents.send('analysis-complete', {
    result: result,
    logFile: result.logFile,
    summaryFile: result.summaryFile
  });
}
```

## 6. 核心功能实现方案

### 6.1 Git 操作集成
```javascript
// 在主进程中使用 simple-git
const simpleGit = require('simple-git');

// 获取提交记录
async function getCommits(projectPath) {
  const git = simpleGit(projectPath);
  const log = await git.log(['--oneline', '--graph', '--decorate', '--all']);
  return log.all.map(commit => ({
    hash: commit.hash,
    date: commit.date,
    message: commit.message,
    author: commit.author_name
  }));
}
```

### 6.2 分析引擎集成

**现有CLI问题：**
- 日志文件名动态生成（包含时间戳），UI无法预知文件路径
- 没有实时日志回调，只能事后读取文件

**改造方案：**

```javascript
// 修改CLI工具，添加回调支持
const { processSummary } = require('./core/src/cli.js');

async function startAnalysis(projectPath, startCommit, endCommit) {
  try {
    const result = await processSummary(projectPath, startCommit, endCommit, {
      // 添加回调函数
      onLog: (logMessage) => {
        mainWindow.webContents.send('analysis-log', logMessage);
      },
      onLogFileCreated: (logFilePath) => {
        // 通知UI日志文件路径
        mainWindow.webContents.send('log-file-created', logFilePath);
      }
    });
    
    // 返回完整结果信息
    mainWindow.webContents.send('analysis-complete', {
      result: result.data,
      logFile: result.logFile,
      summaryFile: result.summaryFile
    });
  } catch (error) {
    mainWindow.webContents.send('analysis-error', error.message);
  }
}
```

**CLI改造的具体位置：**
1. 修改 `src/summary/logger.js` 添加事件回调
2. 修改 `src/summary/summaryProcessor.js` 传递回调函数
3. 返回文件路径信息

### 6.3 配置管理
```javascript
// 配置文件读写
const fs = require('fs');
const path = require('path');

const configPath = path.join(__dirname, 'config.json');

function saveConfig(config) {
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
}

function loadConfig() {
  if (fs.existsSync(configPath)) {
    return JSON.parse(fs.readFileSync(configPath, 'utf8'));
  }
  return getDefaultConfig();
}
```



## 5. 技术实现思路

### 5.1 项目依赖
主要依赖包括：Vue 3、Vue Router、Element Plus、Less、Electron、electron-builder、Vite、simple-git 等。具体版本在实现时根据最新稳定版本确定。

### 5.2 Electron 主进程
- 创建主窗口，配置安全的 webPreferences
- 处理 IPC 通信，调用 Git 操作和 CLI 工具
- 管理文件系统操作和配置文件读写

### 5.3 预加载脚本
- 使用 contextBridge 安全地暴露 API 给渲染进程
- 封装 IPC 通信方法，提供统一的接口
- 处理事件监听和回调函数

## 7. CLI改造需求详细说明

### 7.1 现有CLI的问题

1. **日志文件路径不可预知**
   - 文件名包含时间戳：`project-2024-01-15T10-30-15-123Z-abc1234-def5678.log`
   - UI无法提前知道文件路径，无法读取日志

2. **缺少实时反馈机制**
   - 没有进度回调
   - 没有实时日志流
   - UI只能等待分析完成

3. **结果文件路径问题**
   - 同样包含时间戳，UI无法预知路径

### 7.2 改造方案（简化版）

**采用最小侵入式改造，避免修改现有核心代码：**

1. **新增包装函数（推荐方案）**
```javascript
// 在 src/cli.js 中新增，不修改现有函数
export async function processSummaryForElectron(repoPath, startCommit, endCommit, callbacks = {}) {
  // 设置日志回调（如果提供）
  if (callbacks.onLog) {
    // 通过现有logger系统实现回调
    logger.setRealTimeCallback(callbacks.onLog);
  }

  // 调用现有函数
  const result = await processSummary(repoPath, startCommit, endCommit);

  // 获取生成的文件路径
  const logFile = logger.getCurrentLogFile();
  const summaryFile = getSummaryFilePath(repoPath, startCommit, endCommit);

  // 返回增强的结果
  return {
    ...result,
    files: {
      logFile,
      summaryFile
    }
  };
}
```

2. **利用现有logger系统**
```javascript
// 在 src/summary/logger.js 中添加回调支持
let _realTimeCallback = null;

export function setRealTimeCallback(callback) {
  _realTimeCallback = callback;
}

// 修改现有log方法，添加回调触发
function log(message) {
  // 现有逻辑保持不变
  console.log(message);
  writeToFile(message);

  // 新增：触发实时回调
  if (_realTimeCallback) {
    _realTimeCallback(message);
  }
}
```

3. **文件路径获取工具**
```javascript
// 利用现有的 generateFileName 函数
import { generateFileName } from './summary/fileNameUtils.js';

function getSummaryFilePath(repoPath, startCommit, endCommit) {
  const fileName = generateFileName(repoPath, startCommit, endCommit, 'md');
  return path.join(process.cwd(), 'summary', fileName);
}
```

**优势：**
- 现有代码完全不需要修改
- 保持向后兼容性
- 降低引入bug的风险
- 便于测试和调试

## 8. 开发阶段规划（优化版）

### 阶段一：MVP快速验证（1-2天）
**目标：最快速度实现可用的UI，验证可行性**
1. 在 `electron-app/` 目录下初始化基础 Electron + Vue 项目
2. 实现最简单的项目选择和分析触发功能
3. 集成现有CLI，显示分析结果（不需要实时日志）
4. 验证技术方案可行性

**验收标准：**
- 能选择Git项目文件夹
- 能输入两个commit ID
- 能触发分析并显示结果

### 阶段二：核心功能完善（2-3天）
**目标：实现主要功能，提升用户体验**
1. 集成 simple-git，自动获取并显示提交记录列表
2. 实现提交记录的可视化选择（双选机制）
3. 添加实时日志显示功能
4. 完善错误处理和用户反馈

**验收标准：**
- 自动加载Git提交历史
- 可视化选择起始和结束commit
- 实时显示分析进度和日志
- 友好的错误提示

### 阶段三：用户体验优化（1-2天）
**目标：完善界面和交互，准备发布**
1. 实现配置页面（LLM配置等）
2. 添加结果导出功能（复制、保存MD、保存PDF）
3. 界面美化和交互优化
4. 应用打包和基础测试

**验收标准：**
- 配置管理功能完整
- 结果导出功能可用
- 界面美观易用
- 可以打包成可执行文件

### 阶段四：扩展和优化（按需）
**目标：根据测试反馈进行优化**
1. 性能优化和bug修复
2. 添加更多便民功能
3. 完善文档和帮助信息
4. 考虑添加更多配置选项

## 9. 总结

这个设计方案具有以下特点：

### 优势
- **简单易懂**：只有两个主要页面，功能清晰
- **快速开发**：技术栈成熟，开发效率高
- **易于维护**：代码结构简单，便于后期扩展
- **用户友好**：图形化界面，操作直观

### 核心功能
- Git 项目选择和提交记录展示
- 双提交选择机制
- 实时日志显示
- 结果展示和导出
- 配置管理

### 扩展性
- 后期可以根据需要添加更多功能
- 可以引入状态管理库
- 可以添加更多的 UI 组件
- 可以增加更多的配置选项

这个方案为快速原型开发提供了良好的基础，同时保持了足够的灵活性以支持未来的功能扩展。

## 10. 立即开始实施建议

### 10.1 第一步：创建Electron应用骨架

```bash
# 在项目根目录下创建electron-app目录
mkdir electron-app
cd electron-app

# 初始化package.json
npm init -y

# 安装核心依赖
npm install electron vue@next vue-router@4 element-plus
npm install -D vite @vitejs/plugin-vue electron-builder

# 安装Git操作库
npm install simple-git
```

### 10.2 第二步：创建最小可用版本

**创建基础文件结构：**
```
electron-app/
├── package.json
├── electron.js          # 主进程（约50行代码）
├── preload.js           # 预加载脚本（约20行代码）
├── public/index.html    # HTML模板（约30行代码）
└── src/
    ├── main.js          # Vue入口（约20行代码）
    ├── App.vue          # 根组件（约100行代码）
    └── utils/
        └── electron-api.js  # API封装（约30行代码）
```

**预计开发时间：半天到1天**

### 10.3 第三步：集成现有CLI

```javascript
// 在electron.js中集成现有CLI
const { processSummary } = require('../src/cli.js');

// 处理分析请求
ipcMain.handle('start-analysis', async (event, repoPath, startCommit, endCommit) => {
  try {
    const result = await processSummary(repoPath, startCommit, endCommit);
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});
```

### 10.4 关键技术决策

1. **先实现功能，后优化体验**：第一版不需要实时日志，直接显示最终结果即可
2. **复用现有配置**：直接读取现有的配置文件，不需要重新实现配置管理
3. **简化Git操作**：第一版可以手动输入commit ID，后续再添加可视化选择
4. **最小化依赖**：只安装必需的包，避免过度工程化

### 10.5 风险控制

1. **独立开发**：在`electron-app/`目录下独立开发，不影响现有CLI功能
2. **渐进集成**：先验证基础功能，再逐步添加高级特性
3. **保持兼容**：确保现有CLI工具继续正常工作
4. **快速迭代**：每个阶段都有可用的版本，便于测试和反馈

**总结：这个修改后的方案更加务实，风险更低，可以快速验证UI可行性，建议立即开始第一阶段的开发。**