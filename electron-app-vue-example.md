# Vue组件示例代码

## App.vue (根组件)

```vue
<template>
  <div id="app">
    <el-container>
      <el-header>
        <h1>AI 代码分析工具</h1>
      </el-header>
      
      <el-main>
        <!-- 项目选择区域 -->
        <el-card class="section-card">
          <template #header>
            <span>📁 项目选择</span>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="18">
              <el-input 
                v-model="projectPath" 
                placeholder="请选择Git项目文件夹"
                readonly
              />
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="selectProject">
                选择文件夹
              </el-button>
            </el-col>
          </el-row>
        </el-card>

        <!-- 提交选择区域 -->
        <el-card class="section-card">
          <template #header>
            <span>📋 提交记录选择</span>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-input 
                v-model="startCommit" 
                placeholder="起始 commit ID"
              />
            </el-col>
            <el-col :span="12">
              <el-input 
                v-model="endCommit" 
                placeholder="结束 commit ID"
              />
            </el-col>
          </el-row>
        </el-card>

        <!-- 操作区域 -->
        <el-card class="section-card">
          <template #header>
            <span>🚀 操作区域</span>
          </template>
          
          <el-button 
            type="success" 
            size="large"
            :loading="isAnalyzing"
            :disabled="!canStartAnalysis"
            @click="startAnalysis"
          >
            {{ isAnalyzing ? '分析中...' : '开始分析' }}
          </el-button>
        </el-card>

        <!-- 结果显示区域 -->
        <el-card class="section-card" v-if="analysisResult">
          <template #header>
            <span>📄 分析结果</span>
          </template>
          
          <div v-if="analysisResult.success">
            <el-alert 
              title="分析完成" 
              type="success" 
              :closable="false"
              style="margin-bottom: 20px"
            />
            
            <!-- 这里显示分析结果 -->
            <div class="result-content">
              <pre>{{ JSON.stringify(analysisResult.data, null, 2) }}</pre>
            </div>
          </div>
          
          <div v-else>
            <el-alert 
              :title="analysisResult.error" 
              type="error" 
              :closable="false"
            />
          </div>
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';

export default {
  name: 'App',
  setup() {
    // 响应式数据
    const projectPath = ref('');
    const startCommit = ref('');
    const endCommit = ref('');
    const isAnalyzing = ref(false);
    const analysisResult = ref(null);

    // 计算属性
    const canStartAnalysis = computed(() => {
      return projectPath.value && startCommit.value && endCommit.value && !isAnalyzing.value;
    });

    // 方法
    const selectProject = async () => {
      try {
        const folderPath = await window.electronAPI.selectFolder();
        if (folderPath) {
          projectPath.value = folderPath;
          ElMessage.success('项目文件夹选择成功');
        }
      } catch (error) {
        ElMessage.error('选择文件夹失败: ' + error.message);
      }
    };

    const startAnalysis = async () => {
      if (!canStartAnalysis.value) return;

      isAnalyzing.value = true;
      analysisResult.value = null;

      try {
        ElMessage.info('开始分析，请稍候...');
        
        const result = await window.electronAPI.startAnalysis(
          projectPath.value,
          startCommit.value,
          endCommit.value
        );
        
        analysisResult.value = result;
        
        if (result.success) {
          ElMessage.success('分析完成！');
        } else {
          ElMessage.error('分析失败: ' + result.error);
        }
      } catch (error) {
        ElMessage.error('分析过程出错: ' + error.message);
        analysisResult.value = {
          success: false,
          error: error.message
        };
      } finally {
        isAnalyzing.value = false;
      }
    };

    return {
      projectPath,
      startCommit,
      endCommit,
      isAnalyzing,
      analysisResult,
      canStartAnalysis,
      selectProject,
      startAnalysis
    };
  }
};
</script>

<style scoped>
#app {
  height: 100vh;
}

.el-header {
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
}

.el-header h1 {
  margin: 0;
}

.section-card {
  margin-bottom: 20px;
}

.result-content {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
}

.result-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
```

## 使用说明

这个Vue组件提供了：

1. **项目选择**：点击按钮选择Git项目文件夹
2. **提交输入**：手动输入起始和结束commit ID
3. **分析触发**：点击按钮开始分析
4. **结果显示**：显示分析结果或错误信息

## 下一步优化

1. **美化结果显示**：将JSON结果格式化为更友好的界面
2. **添加Git提交列表**：自动获取并显示提交记录供选择
3. **实时日志**：显示分析过程中的实时日志
4. **结果导出**：添加复制、保存等功能
