# 提示词构建模块 (prompt.js)

## 模块用途

提示词构建模块负责为 AI 分析生成高质量的提示词，提供：
- 结构化提示词构建
- 上下文信息注入
- Token 长度控制和压缩
- 项目类型特定优化

## 主要函数

### buildEnhancedPrompt(commitInfo, analysisData, config)

构建增强版 AI 提示词，是模块的核心函数。

**参数：**
- `commitInfo` (Object): commit 信息
- `analysisData` (Object): 分析数据
- `config` (Object): 配置信息

**返回值：**
- `string`: 构建好的提示词文本

**构建流程：**
1. 加载提示词模板
2. 注入分析结果和上下文信息
3. 根据项目类型调整提示词策略
4. 验证和压缩提示词长度
5. 返回最终提示词

### loadPromptTemplate()

加载提示词模板文件。

**返回值：**
- `string`: 模板内容

**模板加载策略：**
- 优先加载 `templates/prompt-template.md`
- 失败时使用 `templates/default-template.md`
- 最后使用内置的基础模板

### injectContextData(template, commitInfo, analysisData, config)

向模板注入上下文数据。

**参数：**
- `template` (string): 提示词模板
- `commitInfo` (Object): commit 信息
- `analysisData` (Object): 分析数据
- `config` (Object): 配置信息

**返回值：**
- `string`: 注入数据后的提示词

**注入的数据类型：**
- 项目基本信息（类型、技术栈）
- 变更统计信息
- 文件详情和代码差异
- 依赖和配置变更
- 风险评估结果

### adjustPromptByProjectType(prompt, projectType)

根据项目类型调整提示词策略。

**参数：**
- `prompt` (string): 基础提示词
- `projectType` (string): 项目类型

**返回值：**
- `string`: 调整后的提示词

**支持的项目类型：**
- `'react'`: React 项目特定指导
- `'vue'`: Vue 项目特定指导
- `'node'`: Node.js 项目特定指导
- `'nextjs'`: Next.js 项目特定指导

### validateTokenLength(text, maxTokens, model)

验证提示词的 token 长度。

**参数：**
- `text` (string): 要验证的文本
- `maxTokens` (number): 最大 token 数
- `model` (string): 模型名称

**返回值：**
```javascript
{
  tokenCount: number,      // 实际 token 数
  maxTokens: number,       // 最大 token 数
  isValid: boolean,        // 是否在限制内
  excess: number,          // 超出的 token 数
  percentage: string       // 使用百分比
}
```

### compressPrompt(prompt, maxTokens, model)

压缩提示词以适应 token 限制。

**参数：**
- `prompt` (string): 原始提示词
- `maxTokens` (number): 目标 token 数
- `model` (string): 模型名称

**返回值：**
- `string`: 压缩后的提示词

**压缩策略：**
1. 压缩代码差异部分（保留前 50%）
2. 简化文件详情描述
3. 移除次要的统计信息
4. 保留核心分析数据

## 项目类型检测

### detectProjectType(analysisData, config)

检测项目类型。

**参数：**
- `analysisData` (Object): 分析数据
- `config` (Object): 配置信息

**返回值：**
- `string`: 项目类型

**检测逻辑：**
- 优先使用配置中指定的类型
- 基于依赖包检测框架
- 分析文件结构和命名模式
- 检查特定的配置文件

### detectTechStack(analysisData, config)

检测技术栈。

**返回值：**
- `string`: 技术栈描述

**检测内容：**
- 前端框架（React, Vue, Angular）
- 构建工具（Webpack, Vite, Rollup）
- 语言类型（JavaScript, TypeScript）
- 样式方案（CSS, Sass, Tailwind）

## 数据注入函数

### injectFileDetails(prompt, analysisData)

注入文件详情信息。

**功能：**
- 生成文件变更详情
- 包含文件角色和重要性
- 添加代码结构信息
- 过滤不重要的文件

### injectDependencyChanges(prompt, analysisData)

注入依赖和配置变更信息。

**功能：**
- 包依赖变更详情
- 配置文件修改信息
- 影响评估结果

### injectCodeDiffs(prompt, analysisData)

注入代码差异信息。

**功能：**
- 选择重要文件的 diff
- 增强差异上下文信息
- 控制 diff 内容长度

### enhanceDiffContext(file)

增强代码差异的上下文信息。

**参数：**
- `file` (Object): 文件信息

**返回值：**
- `string`: 增强后的差异信息

**增强内容：**
- 文件类型和用途说明
- 变更摘要
- 影响的函数/组件信息

## 辅助函数

### extractAffectedComponents(diff)

提取受影响的组件/函数名。

**参数：**
- `diff` (string): 差异内容

**返回值：**
- `Array<string>`: 受影响的组件/函数列表

### generateChangeSummary(file)

生成文件变更摘要。

**参数：**
- `file` (Object): 文件信息

**返回值：**
- `string`: 变更摘要描述

### getFileTypeDescription(filePath)

获取文件类型描述。

**参数：**
- `filePath` (string): 文件路径

**返回值：**
- `string`: 文件类型的中文描述

### cleanEmptyFields(prompt)

清理提示词中的空字段。

**功能：**
- 移除显示为"无"的字段行
- 清理空的代码结构信息块
- 压缩连续的空行
- 清理行尾空白字符

### isImportantFile(filePath, config)

判断文件是否重要。

**参数：**
- `filePath` (string): 文件路径
- `config` (Object): 配置信息

**返回值：**
- `boolean`: 是否为重要文件

**判断依据：**
- 配置的重要文件模式
- 配置的不重要文件模式
- 文件扩展名和路径模式

## 模板系统

### 模板变量

提示词模板支持以下变量替换：

**基本信息变量：**
- `{projectType}`: 项目类型（如：react, vue, node）
- `{techStack}`: 技术栈描述（如：React, TypeScript, SCSS）
- `{startCommit}`: 起始 commit ID
- `{endCommit}`: 结束 commit ID
- `{timeSpan}`: 时间跨度描述
- `{authors}`: 参与作者列表（逗号分隔）

**统计信息变量：**
- `{totalFiles}`: 总变更文件数
- `{totalAddedLines}`: 总新增行数
- `{totalDeletedLines}`: 总删除行数
- `{totalModifiedLines}`: 总修改行数
- `{fileTypes}`: 文件类型分布（如：js: 5, vue: 3）

**文件列表变量：**
- `{allChangedFiles.join(', ') || '无'}`: 所有变更文件路径
- `{newFiles.join(', ') || '无'}`: 新增文件路径
- `{deletedFiles.join(', ') || '无'}`: 删除文件路径

**复杂内容变量：**
- `{# each parsed_files #}...{/each}`: 文件详情循环块
- `{# if has_dependency_changes #}...{/if}`: 依赖变更条件块
- `{filesData.map(file => file.diff).join('\n\n')}`: 代码差异内容

### 项目指导文件

针对不同项目类型提供专门的分析指导：

- `templates/project-guidance/react.md`: React 项目指导
- `templates/project-guidance/vue.md`: Vue 项目指导
- `templates/project-guidance/node.md`: Node.js 项目指导
- `templates/project-guidance/nextjs.md`: Next.js 项目指导

## Token 管理

模块集成了精确的 token 计算功能：

- 使用 tiktoken 库进行精确计算
- 支持不同模型的 token 计算
- 提供 token 使用统计
- 实现智能压缩策略

压缩时优先保留：
1. 核心分析结果
2. 重要文件变更
3. 风险评估信息
4. 项目上下文
