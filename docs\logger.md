# 日志管理模块 (logger.js)

## 模块用途

日志管理模块提供统一的日志记录功能，支持：
- 同时输出到控制台和文件
- 多种日志级别和格式
- 自动文件管理和时间戳
- 错误堆栈跟踪

## 主要函数

### initLogFile(repoPath, startCommit, endCommit)

初始化日志文件。

**参数：**
- `repoPath` (string): 仓库路径
- `startCommit` (string): 起始 commit
- `endCommit` (string): 结束 commit

**返回值：**
- `string`: 日志文件名

**功能：**
- 生成唯一的日志文件名
- 创建 logs 目录（如果不存在）
- 写入文件头信息
- 设置日志文件路径

### log(message, consoleOnly)

记录普通日志。

**参数：**
- `message` (string): 日志消息
- `consoleOnly` (boolean): 是否仅输出到控制台

**功能：**
- 输出到控制台
- 写入日志文件（带时间戳）
- 处理写入错误

### error(message, error)

记录错误日志。

**参数：**
- `message` (string): 错误消息
- `error` (Error): 错误对象（可选）

**功能：**
- 添加错误标识符（❌）
- 记录错误堆栈信息
- 格式化错误输出

### warn(message)

记录警告日志。

**参数：**
- `message` (string): 警告消息

**功能：**
- 添加警告标识符（⚠️）
- 突出显示警告信息

### success(message)

记录成功日志。

**参数：**
- `message` (string): 成功消息

**功能：**
- 添加成功标识符（✅）
- 标记操作成功完成

### info(message)

记录信息日志。

**参数：**
- `message` (string): 信息消息

**功能：**
- 添加信息标识符（📋）
- 记录重要信息

### separator(title, char, length)

记录分隔线。

**参数：**
- `title` (string): 分隔线标题（可选）
- `char` (string): 分隔字符（默认 '='）
- `length` (number): 分隔线长度（默认 80）

**功能：**
- 生成格式化的分隔线
- 支持居中标题
- 用于分段显示

### json(title, data)

记录 JSON 数据。

**参数：**
- `title` (string): 数据标题
- `data` (Object): 要记录的数据

**功能：**
- 格式化 JSON 输出
- 添加数据标识符（📊）
- 美化显示结构化数据

### finish()

完成日志记录。

**功能：**
- 写入结尾信息
- 显示日志文件路径
- 关闭日志会话

### getLogFile()

获取当前日志文件路径。

**返回值：**
- `string|null`: 日志文件路径

## 日志格式

### 控制台输出

使用 emoji 和颜色标识不同类型的日志：
- 📋 普通信息
- ✅ 成功操作
- ⚠️ 警告信息
- ❌ 错误信息
- 📊 数据输出

### 文件输出

包含详细的时间戳和上下文信息：
```
[2024-08-01 15:30:45] 📋 开始分析代码变更...
[2024-08-01 15:30:46] ✅ Git 数据获取完成
[2024-08-01 15:30:47] ⚠️ 部分文件解析失败
```

## 文件管理

### 文件命名

日志文件使用以下命名格式：
```
{projectName}-{timestamp}-{shortStart}-{shortEnd}.log
```

- `projectName`: 项目目录名称（特殊字符被清理为连字符）
- `timestamp`: ISO 8601 格式的时间戳（冒号和点替换为连字符）
- `shortStart`: 起始 commit 的前 7 位
- `shortEnd`: 结束 commit 的前 7 位

### 目录结构

```
logs/
├── my-project-2024-08-01T15-30-45-123Z-abc123-def456.log
├── my-project-2024-08-01T16-15-20-456Z-def456-ghi789.log
└── ...
```

### 文件头信息

每个日志文件包含详细的头信息：
```
================================================================================
AI Analyzer - 代码变更分析报告
================================================================================
生成时间: 2024/8/1 15:30:45
仓库路径: /path/to/repo
对比范围: abc123 -> def456
日志文件: my-project-2024-08-01T15-30-45-123Z-abc123-def456.log
================================================================================
```

## 错误处理

### 文件写入错误

- 权限不足：显示权限错误提示
- 磁盘空间不足：显示空间不足警告
- 路径不存在：自动创建目录

### 编码问题

- 使用 UTF-8 编码
- 处理特殊字符
- 避免编码冲突

## 性能优化

### 异步写入

- 使用同步写入确保日志完整性
- 批量写入减少 I/O 操作
- 缓冲区管理优化性能

### 内存管理

- 及时释放大对象
- 避免内存泄漏
- 控制日志文件大小

## 配置选项

### 日志级别

支持不同的日志级别控制：
- `debug`: 调试信息
- `info`: 一般信息
- `warn`: 警告信息
- `error`: 错误信息

### 输出控制

- `consoleOutput`: 是否输出到控制台
- `fileOutput`: 是否输出到文件
- `timestampFormat`: 时间戳格式
- `maxFileSize`: 最大文件大小

## 使用示例

```javascript
const {logger} = require('./logger');

// 初始化日志
logger.initLogFile('/path/to/repo', 'abc123', 'def456');

// 记录不同类型的日志
logger.info('开始分析');
logger.success('操作成功');
logger.warn('发现警告');
logger.error('操作失败', new Error('详细错误'));

// 记录结构化数据
logger.json('分析结果', { files: 10, lines: 1000 });

// 添加分隔线
logger.separator('分析完成');

// 完成日志记录
logger.finish();
```
